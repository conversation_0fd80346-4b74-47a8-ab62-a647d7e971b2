package com.writing.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.writing.entity.NovelGenre;

import java.util.List;

/**
 * 小说类型Service接口
 */
public interface NovelGenreService extends IService<NovelGenre> {

    /**
     * 获取用户的小说类型列表（包含默认类型）
     */
    List<NovelGenre> getGenresByUserId(Long userId);

    /**
     * 根据ID获取小说类型
     */
    NovelGenre getGenreById(Long genreId, Long userId);

    /**
     * 根据代码获取小说类型
     */
    NovelGenre getGenreByCode(String code, Long userId);

    /**
     * 创建小说类型
     */
    NovelGenre createGenre(NovelGenre genre);

    /**
     * 更新小说类型
     */
    NovelGenre updateGenre(NovelGenre genre);

    /**
     * 删除小说类型
     */
    boolean deleteGenre(Long genreId, Long userId);

    /**
     * 初始化用户的默认类型
     */
    void initDefaultGenres(Long userId);

    /**
     * 获取默认类型列表
     */
    List<NovelGenre> getDefaultGenres();
}
