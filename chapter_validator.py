#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
章节切分结果验证模块
提供切分质量评估和问题检测功能
"""

import statistics
from typing import List, Dict, Tuple
from dataclasses import dataclass
from chapter_splitter import Chapter, NovelSplitResult


@dataclass
class ValidationResult:
    """验证结果"""
    overall_score: float  # 总体评分 0-100
    issues: List[str]  # 发现的问题
    suggestions: List[str]  # 改进建议
    statistics: Dict  # 统计信息
    quality_metrics: Dict  # 质量指标


class ChapterValidator:
    """章节验证器"""
    
    def __init__(self):
        self.min_chapter_length = 100  # 最小章节长度
        self.max_chapter_length = 50000  # 最大章节长度
        self.ideal_chapter_count_range = (5, 200)  # 理想章节数量范围
        
    def validate(self, result: NovelSplitResult) -> ValidationResult:
        """
        验证章节切分结果
        
        Args:
            result: 章节切分结果
            
        Returns:
            ValidationResult: 验证结果
        """
        issues = []
        suggestions = []
        
        # 基础检查
        basic_issues = self._check_basic_structure(result)
        issues.extend(basic_issues)
        
        # 长度分布检查
        length_issues, length_suggestions = self._check_length_distribution(result.chapters)
        issues.extend(length_issues)
        suggestions.extend(length_suggestions)
        
        # 重叠和遗漏检查
        overlap_issues = self._check_overlaps_and_gaps(result.chapters, result.total_words)
        issues.extend(overlap_issues)
        
        # 标题质量检查
        title_issues, title_suggestions = self._check_title_quality(result.chapters)
        issues.extend(title_issues)
        suggestions.extend(title_suggestions)
        
        # 章节顺序检查
        order_issues = self._check_chapter_order(result.chapters)
        issues.extend(order_issues)
        
        # 计算统计信息
        statistics_info = self._calculate_statistics(result.chapters)
        
        # 计算质量指标
        quality_metrics = self._calculate_quality_metrics(result, issues)
        
        # 计算总体评分
        overall_score = self._calculate_overall_score(result, issues, quality_metrics)
        
        # 生成改进建议
        if overall_score < 70:
            suggestions.extend(self._generate_improvement_suggestions(result, issues))
        
        return ValidationResult(
            overall_score=overall_score,
            issues=issues,
            suggestions=suggestions,
            statistics=statistics_info,
            quality_metrics=quality_metrics
        )
    
    def _check_basic_structure(self, result: NovelSplitResult) -> List[str]:
        """检查基础结构"""
        issues = []
        
        if not result.chapters:
            issues.append("没有找到任何章节")
            return issues
        
        if result.total_chapters != len(result.chapters):
            issues.append(f"章节数量不一致：声明{result.total_chapters}个，实际{len(result.chapters)}个")
        
        if result.total_chapters < 2:
            issues.append("章节数量过少，可能切分不准确")
        elif result.total_chapters > 500:
            issues.append("章节数量过多，可能过度切分")
        
        return issues
    
    def _check_length_distribution(self, chapters: List[Chapter]) -> Tuple[List[str], List[str]]:
        """检查长度分布"""
        issues = []
        suggestions = []
        
        if not chapters:
            return issues, suggestions
        
        lengths = [ch.word_count for ch in chapters]
        
        # 检查过短章节
        short_chapters = [i for i, length in enumerate(lengths) if length < self.min_chapter_length]
        if short_chapters:
            issues.append(f"发现{len(short_chapters)}个过短章节（少于{self.min_chapter_length}字）")
            suggestions.append("考虑合并过短的章节")
        
        # 检查过长章节
        long_chapters = [i for i, length in enumerate(lengths) if length > self.max_chapter_length]
        if long_chapters:
            issues.append(f"发现{len(long_chapters)}个过长章节（超过{self.max_chapter_length}字）")
            suggestions.append("考虑拆分过长的章节")
        
        # 检查长度变异性
        if len(lengths) > 1:
            mean_length = statistics.mean(lengths)
            stdev_length = statistics.stdev(lengths)
            cv = stdev_length / mean_length if mean_length > 0 else 0
            
            if cv > 1.5:
                issues.append(f"章节长度变异过大（变异系数：{cv:.2f}）")
                suggestions.append("章节长度分布不均匀，建议重新调整切分点")
        
        return issues, suggestions
    
    def _check_overlaps_and_gaps(self, chapters: List[Chapter], total_words: int) -> List[str]:
        """检查重叠和遗漏"""
        issues = []
        
        if not chapters:
            return issues
        
        # 按起始位置排序
        sorted_chapters = sorted(chapters, key=lambda x: x.start_position)
        
        # 检查重叠
        for i in range(len(sorted_chapters) - 1):
            current = sorted_chapters[i]
            next_chapter = sorted_chapters[i + 1]
            
            if current.end_position > next_chapter.start_position:
                overlap = current.end_position - next_chapter.start_position
                issues.append(f"章节{current.chapter_number}和{next_chapter.chapter_number}重叠{overlap}个字符")
        
        # 检查遗漏
        for i in range(len(sorted_chapters) - 1):
            current = sorted_chapters[i]
            next_chapter = sorted_chapters[i + 1]
            
            if current.end_position < next_chapter.start_position:
                gap = next_chapter.start_position - current.end_position
                if gap > 10:  # 允许小的间隙
                    issues.append(f"章节{current.chapter_number}和{next_chapter.chapter_number}之间遗漏{gap}个字符")
        
        # 检查总长度
        if sorted_chapters:
            covered_length = sorted_chapters[-1].end_position - sorted_chapters[0].start_position
            if abs(covered_length - total_words) > total_words * 0.1:  # 允许10%的误差
                issues.append(f"章节总长度与原文长度不匹配：章节{covered_length}字，原文{total_words}字")
        
        return issues
    
    def _check_title_quality(self, chapters: List[Chapter]) -> Tuple[List[str], List[str]]:
        """检查标题质量"""
        issues = []
        suggestions = []
        
        if not chapters:
            return issues, suggestions
        
        # 检查空标题
        empty_titles = [ch.chapter_number for ch in chapters if not ch.title.strip()]
        if empty_titles:
            issues.append(f"发现{len(empty_titles)}个空标题章节")
            suggestions.append("为空标题章节添加默认标题")
        
        # 检查重复标题
        titles = [ch.title for ch in chapters if ch.title.strip()]
        duplicate_titles = []
        seen_titles = set()
        for title in titles:
            if title in seen_titles:
                duplicate_titles.append(title)
            seen_titles.add(title)
        
        if duplicate_titles:
            issues.append(f"发现{len(duplicate_titles)}个重复标题")
            suggestions.append("修改重复的章节标题")
        
        # 检查标题长度
        long_titles = [ch.chapter_number for ch in chapters if len(ch.title) > 100]
        if long_titles:
            issues.append(f"发现{len(long_titles)}个过长标题（超过100字符）")
            suggestions.append("缩短过长的章节标题")
        
        return issues, suggestions
    
    def _check_chapter_order(self, chapters: List[Chapter]) -> List[str]:
        """检查章节顺序"""
        issues = []
        
        if not chapters:
            return issues
        
        # 检查章节号顺序
        chapter_numbers = [ch.chapter_number for ch in chapters]
        
        # 检查是否有重复章节号
        if len(set(chapter_numbers)) != len(chapter_numbers):
            issues.append("存在重复的章节号")
        
        # 检查是否连续
        sorted_numbers = sorted(chapter_numbers)
        expected_numbers = list(range(1, len(chapters) + 1))
        
        if sorted_numbers != expected_numbers:
            issues.append("章节号不连续或不从1开始")
        
        # 检查位置顺序
        positions = [ch.start_position for ch in chapters]
        if positions != sorted(positions):
            issues.append("章节位置顺序混乱")
        
        return issues
    
    def _calculate_statistics(self, chapters: List[Chapter]) -> Dict:
        """计算统计信息"""
        if not chapters:
            return {}
        
        lengths = [ch.word_count for ch in chapters]
        confidences = [ch.confidence for ch in chapters]
        
        return {
            "total_chapters": len(chapters),
            "total_words": sum(lengths),
            "avg_chapter_length": statistics.mean(lengths),
            "median_chapter_length": statistics.median(lengths),
            "min_chapter_length": min(lengths),
            "max_chapter_length": max(lengths),
            "length_std_dev": statistics.stdev(lengths) if len(lengths) > 1 else 0,
            "avg_confidence": statistics.mean(confidences),
            "min_confidence": min(confidences),
            "max_confidence": max(confidences)
        }
    
    def _calculate_quality_metrics(self, result: NovelSplitResult, issues: List[str]) -> Dict:
        """计算质量指标"""
        metrics = {}
        
        # 基础指标
        metrics["chapter_count_score"] = self._score_chapter_count(result.total_chapters)
        metrics["length_distribution_score"] = self._score_length_distribution(result.chapters)
        metrics["title_quality_score"] = self._score_title_quality(result.chapters)
        metrics["confidence_score"] = result.confidence * 100
        metrics["issue_penalty"] = len(issues) * 5  # 每个问题扣5分
        
        return metrics
    
    def _score_chapter_count(self, count: int) -> float:
        """评分章节数量"""
        min_ideal, max_ideal = self.ideal_chapter_count_range
        
        if min_ideal <= count <= max_ideal:
            return 100.0
        elif count < min_ideal:
            return max(0, 100 - (min_ideal - count) * 10)
        else:
            return max(0, 100 - (count - max_ideal) * 2)
    
    def _score_length_distribution(self, chapters: List[Chapter]) -> float:
        """评分长度分布"""
        if not chapters:
            return 0
        
        lengths = [ch.word_count for ch in chapters]
        
        # 检查长度合理性
        reasonable_count = sum(1 for length in lengths 
                             if self.min_chapter_length <= length <= self.max_chapter_length)
        
        ratio = reasonable_count / len(chapters)
        return ratio * 100
    
    def _score_title_quality(self, chapters: List[Chapter]) -> float:
        """评分标题质量"""
        if not chapters:
            return 0
        
        quality_count = sum(1 for ch in chapters 
                          if ch.title.strip() and len(ch.title) <= 100)
        
        ratio = quality_count / len(chapters)
        return ratio * 100
    
    def _calculate_overall_score(self, result: NovelSplitResult, issues: List[str], 
                               quality_metrics: Dict) -> float:
        """计算总体评分"""
        # 基础分数
        base_score = 60
        
        # 各项指标权重
        weights = {
            "chapter_count_score": 0.2,
            "length_distribution_score": 0.3,
            "title_quality_score": 0.2,
            "confidence_score": 0.3
        }
        
        # 计算加权分数
        weighted_score = 0
        for metric, weight in weights.items():
            if metric in quality_metrics:
                weighted_score += quality_metrics[metric] * weight
        
        # 减去问题惩罚
        penalty = quality_metrics.get("issue_penalty", 0)
        
        final_score = max(0, min(100, base_score + weighted_score - penalty))
        return round(final_score, 1)
    
    def _generate_improvement_suggestions(self, result: NovelSplitResult, 
                                        issues: List[str]) -> List[str]:
        """生成改进建议"""
        suggestions = []
        
        if result.confidence < 0.6:
            suggestions.append("切分置信度较低，建议尝试其他切分方法或手动调整")
        
        if result.total_chapters == 1:
            suggestions.append("只识别出一个章节，建议检查文本格式或使用更宽松的匹配模式")
        
        if "重叠" in str(issues):
            suggestions.append("存在章节重叠，建议调整切分算法的边界检测")
        
        if "遗漏" in str(issues):
            suggestions.append("存在内容遗漏，建议检查切分点的准确性")
        
        return suggestions
