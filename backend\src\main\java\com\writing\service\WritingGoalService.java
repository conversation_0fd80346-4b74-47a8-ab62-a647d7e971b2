package com.writing.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.writing.entity.WritingGoal;

import java.util.List;

/**
 * 写作目标服务接口
 */
public interface WritingGoalService extends IService<WritingGoal> {
    
    /**
     * 根据用户ID获取写作目标列表
     */
    List<WritingGoal> getGoalsByUserId(Long userId);
    
    /**
     * 根据ID和用户ID获取写作目标
     */
    WritingGoal getGoalById(Long goalId, Long userId);
    
    /**
     * 创建写作目标
     */
    WritingGoal createGoal(WritingGoal goal);
    
    /**
     * 更新写作目标
     */
    WritingGoal updateGoal(WritingGoal goal);
    
    /**
     * 更新目标进度
     */
    WritingGoal updateProgress(Long goalId, Long userId, Integer increment, String note);
    
    /**
     * 删除写作目标
     */
    boolean deleteGoal(Long goalId, Long userId);
    
    /**
     * 获取用户的活跃目标数量
     */
    long getActiveGoalsCount(Long userId);
    
    /**
     * 获取用户的已完成目标数量
     */
    long getCompletedGoalsCount(Long userId);
}
