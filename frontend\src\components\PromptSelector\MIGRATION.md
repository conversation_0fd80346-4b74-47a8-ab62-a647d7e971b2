# 提示词选择组件迁移指南

本文档说明如何将现有的提示词选择功能迁移到新的独立组件。

## 迁移概述

原有的提示词选择功能分散在Writer.vue中，包含大量重复代码和复杂的状态管理。新的独立组件提供了：

- 🎯 **统一的API**：一致的接口和使用方式
- 🔧 **解耦设计**：独立的组件和组合式函数
- 📝 **简化使用**：减少样板代码
- 🔄 **更好的复用性**：可在多个页面使用

## 迁移步骤

### 1. 导入新组件

```javascript
// 在需要使用的Vue文件中添加导入
import PromptSelector from '@/components/PromptSelector.vue'
import { usePromptSelector } from '@/composables/usePromptSelector'
```

### 2. 替换原有的对话框

**原有代码：**
```vue
<!-- 原有的提示词选择对话框 -->
<el-dialog v-model="showPromptDialog" title="选择提示词" width="800px">
  <!-- 复杂的提示词列表和变量填充逻辑 -->
</el-dialog>
```

**新代码：**
```vue
<!-- 使用新的提示词选择组件 -->
<PromptSelector
  v-model:visible="promptSelector.visible.value"
  :category="promptSelector.category.value"
  :prompts="promptSelector.availablePrompts.value"
  :auto-fill-data="promptSelector.autoFillData.value"
  @confirm="handlePromptConfirm"
  @cancel="handlePromptCancel"
/>
```

### 3. 初始化组合式函数

**原有代码：**
```javascript
// 分散的状态管理
const showPromptDialog = ref(false)
const selectedPromptCategory = ref('')
const selectedPrompt = ref(null)
const promptVariables = ref({})
const finalPrompt = ref('')
// ... 更多状态
```

**新代码：**
```javascript
// 使用组合式函数统一管理
const promptSelector = usePromptSelector()
```

### 4. 更新打开提示词选择的方法

**原有代码：**
```javascript
const openPromptDialog = (category) => {
  selectedPromptCategory.value = category
  showPromptDialog.value = true
  selectedPrompt.value = null
  promptVariables.value = {}
  // ... 复杂的初始化逻辑
}
```

**新代码：**
```javascript
const openPromptDialog = (category) => {
  // 准备自动填充数据
  const autoFillData = promptSelector.createAutoFillData.character(
    novelInfo,
    characterForm
  )

  // 打开选择器
  promptSelector.openSelector({
    category,
    prompts: availablePrompts.value,
    autoFillData,
    availableContextChapters: contextChapters.value
  })
}
```

### 5. 处理确认和取消事件

**原有代码：**
```javascript
const useSelectedPrompt = () => {
  // 复杂的分支逻辑
  if (selectedPromptCategory.value === 'character') {
    // 人物生成逻辑
  } else if (selectedPromptCategory.value === 'content') {
    // 内容生成逻辑
  }
  // ... 更多分支
}
```

**新代码：**
```javascript
const handlePromptConfirm = (result) => {
  // 统一的处理逻辑
  switch (result.category) {
    case 'character':
      generateCharacter(result.finalPrompt)
      break
    case 'content':
      generateContent(result.finalPrompt)
      break
    // ... 其他分类
  }
  
  promptSelector.closeSelector()
}

const handlePromptCancel = () => {
  promptSelector.closeSelector()
}
```

## 具体迁移示例

### 人物生成功能迁移

**原有代码：**
```javascript
// 打开人物生成提示词选择
const openCharacterPrompt = () => {
  selectedPromptCategory.value = 'character'
  showPromptDialog.value = true
  
  // 手动填充变量
  nextTick(() => {
    if (selectedPrompt.value) {
      promptVariables.value['小说标题'] = currentNovel.value?.title
      promptVariables.value['姓名'] = characterForm.value.name
      // ... 更多手动填充
    }
  })
}

// 使用提示词
const useCharacterPrompt = () => {
  if (selectedPromptCategory.value === 'character') {
    generateCharacterWithPrompt(finalPrompt.value)
  }
  showPromptDialog.value = false
}
```

**新代码：**
```javascript
// 打开人物生成提示词选择
const openCharacterPrompt = () => {
  const autoFillData = promptSelector.createAutoFillData.character(
    currentNovel.value,
    characterForm.value
  )

  promptSelector.openSelector({
    category: 'character',
    prompts: availablePrompts.value,
    autoFillData
  })
}

// 处理确认
const handlePromptConfirm = (result) => {
  if (result.category === 'character') {
    generateCharacterWithPrompt(result.finalPrompt)
  }
  promptSelector.closeSelector()
}
```

### 章节生成功能迁移

**原有代码：**
```javascript
// 复杂的章节生成逻辑
const generateChapterWithPrompt = () => {
  selectedPromptCategory.value = 'content'
  showPromptDialog.value = true
  
  // 复杂的变量填充逻辑
  setTimeout(() => {
    promptVariables.value['章节标题'] = currentChapter.value?.title
    promptVariables.value['章节大纲'] = currentChapter.value?.description
    // ... 更多逻辑
  }, 100)
}
```

**新代码：**
```javascript
// 简化的章节生成逻辑
const generateChapterWithPrompt = () => {
  const autoFillData = promptSelector.createAutoFillData.chapter(
    currentNovel.value,
    currentChapter.value,
    generateConfig.value
  )

  promptSelector.openSelector({
    category: 'content',
    prompts: availablePrompts.value,
    autoFillData,
    availableContextChapters: availableContextChapters.value
  })
}
```

## 迁移检查清单

- [ ] 导入新组件和组合式函数
- [ ] 替换原有的对话框模板
- [ ] 初始化 `usePromptSelector`
- [ ] 更新打开提示词选择的方法
- [ ] 实现 `handlePromptConfirm` 和 `handlePromptCancel`
- [ ] 移除原有的状态管理代码
- [ ] 测试所有提示词选择功能
- [ ] 验证自动填充功能正常工作
- [ ] 确认前文概要选择功能正常

## 注意事项

1. **渐进式迁移**：可以保留原有代码，逐步迁移各个功能
2. **兼容性**：新组件设计时考虑了与原有API的兼容性
3. **测试**：迁移后需要充分测试各种场景
4. **自定义**：如需特殊功能，可以扩展组合式函数

## 迁移后的优势

1. **代码减少**：减少约60%的提示词相关代码
2. **维护性提升**：统一的逻辑，更容易维护
3. **复用性增强**：可在其他页面轻松复用
4. **类型安全**：更好的TypeScript支持
5. **测试友好**：独立组件更容易进行单元测试

## 故障排除

### 常见问题

1. **自动填充不工作**
   - 检查 `autoFillData` 是否正确传递
   - 确认变量名称与提示词中的占位符匹配

2. **前文概要选择异常**
   - 检查 `availableContextChapters` 数据格式
   - 确认章节数据包含必要的字段

3. **样式问题**
   - 检查CSS作用域
   - 确认Element Plus主题正确加载

### 调试技巧

```javascript
// 在控制台查看组合式函数状态
console.log('PromptSelector状态:', {
  visible: promptSelector.visible.value,
  category: promptSelector.category.value,
  selectedPrompt: promptSelector.selectedPrompt.value,
  variables: promptSelector.variables.value
})
```

## 后续优化建议

1. **添加TypeScript类型**：为更好的类型安全
2. **单元测试**：为组件和组合式函数添加测试
3. **性能优化**：大量提示词时的虚拟滚动
4. **国际化**：支持多语言
5. **主题定制**：支持自定义样式主题
