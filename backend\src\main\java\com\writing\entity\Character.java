package com.writing.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 人物实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value = "characters", autoResultMap = true)
public class Character {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    @TableField("novel_id")
    private Long novelId;
    
    @TableField("name")
    private String name;
    
    @TableField("role")
    private String role;
    
    @TableField("gender")
    private String gender;
    
    @TableField("age")
    private Integer age;
    
    @TableField("appearance")
    private String appearance;
    
    @TableField("personality")
    private String personality;
    
    @TableField("background")
    private String background;
    
    @TableField(value = "tags", typeHandler = JacksonTypeHandler.class)
    private List<String> tags;
    
    @TableField("avatar")
    private String avatar;
    
    @TableField("`generated`")
    private Integer generated;
    
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
    
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
    
    @TableLogic
    @TableField("deleted")
    private Integer deleted;
}
