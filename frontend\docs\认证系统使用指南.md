# 认证系统使用指南

## 概述

本项目已经实现了完整的用户认证和自动跳转功能，包括：

- 用户登录状态检查
- Token过期自动检测
- 未登录或Token过期时自动跳转到登录页
- 统一的认证状态管理
- 路由守卫保护

## 核心组件

### 1. 认证Store (`stores/authStore.js`)

集中管理用户认证状态：

```javascript
import { useAuthStore } from '@/stores/authStore'

const authStore = useAuthStore()

// 检查登录状态
console.log(authStore.isLoggedIn)

// 获取用户信息
console.log(authStore.user)

// 登录
await authStore.login(username, password)

// 退出登录
await authStore.logout()
```

### 2. 认证组合式函数 (`composables/useAuth.js`)

提供便捷的认证相关功能：

```javascript
import { useAuth } from '@/composables/useAuth'

const { 
  isLoggedIn, 
  user, 
  login, 
  logout, 
  requireAuth,
  withAuth 
} = useAuth()

// 要求用户登录
if (!requireAuth()) {
  return // 会自动跳转到登录页
}

// 安全执行需要认证的操作
await withAuth(async () => {
  // 这里的代码只有在用户已登录时才会执行
  await someApiCall()
}, { validateToken: true })
```

### 3. 路由守卫 (`router/guards.js`)

自动保护需要认证的路由：

- 未登录用户访问受保护页面时自动跳转到登录页
- 已登录用户访问登录页时自动跳转到首页
- 定期验证Token有效性

### 4. API拦截器 (`services/apiClient.js`)

自动处理API请求中的认证错误：

- 自动添加Authorization头
- 401错误时自动清除认证信息并跳转到登录页
- 统一的错误处理

## 使用方法

### 在组件中检查登录状态

```vue
<template>
  <div v-if="isLoggedIn">
    <p>欢迎，{{ user.username }}！</p>
    <el-button @click="handleLogout">退出登录</el-button>
  </div>
  <div v-else>
    <p>请先登录</p>
  </div>
</template>

<script setup>
import { useAuth } from '@/composables/useAuth'

const { isLoggedIn, user, logout } = useAuth()

const handleLogout = async () => {
  await logout(true) // true表示显示确认对话框
}
</script>
```

### 保护需要认证的操作

```javascript
import { useAuth } from '@/composables/useAuth'

const { withAuth } = useAuth()

const handleSensitiveOperation = async () => {
  await withAuth(async () => {
    // 只有在用户已登录且Token有效时才执行
    await deleteImportantData()
  }, {
    requireLogin: true,
    validateToken: true,
    loginMessage: '此操作需要登录',
    expiredMessage: '登录已过期，请重新登录'
  })
}
```

### 手动验证认证状态

```javascript
import { useAuth } from '@/composables/useAuth'

const { checkAuth, validateAuth } = useAuth()

// 快速检查（仅检查本地状态）
if (!checkAuth()) {
  console.log('用户未登录或Token已过期')
}

// 完整验证（调用API验证Token）
const isValid = await validateAuth()
if (!isValid) {
  console.log('Token无效，用户已被自动登出')
}
```

## 自动跳转机制

### 1. 路由级别保护

- 访问受保护页面时，如果未登录会自动跳转到 `/login`
- 已登录用户访问登录页会自动跳转到 `/`

### 2. API级别保护

- API返回401错误时自动清除认证信息并跳转到登录页
- 支持后端返回的标准错误格式和HTTP状态码

### 3. Token过期检查

- 前端简单检查：基于登录时间计算（7天过期）
- 后端验证：定期调用API验证Token有效性
- 智能验证：避免每次路由都调用API，仅在必要时验证

## 配置选项

### 公开页面配置

在 `router/guards.js` 中修改不需要认证的页面：

```javascript
const publicPages = ['/login', '/register', '/about']
```

### Token过期时间

在 `services/authApi.js` 中修改前端Token过期检查时间：

```javascript
const expireTime = 7 * 24 * 60 * 60 * 1000 // 7天
```

### API验证频率

在 `router/guards.js` 中修改Token验证间隔：

```javascript
const fiveMinutes = 5 * 60 * 1000 // 5分钟
```

## 最佳实践

1. **使用组合式函数**：优先使用 `useAuth()` 而不是直接操作store
2. **保护敏感操作**：使用 `withAuth()` 包装需要认证的操作
3. **避免重复验证**：系统已经自动处理大部分认证检查
4. **错误处理**：认证相关错误会自动显示用户友好的消息
5. **状态同步**：认证状态在整个应用中自动同步

## 故障排除

### 常见问题

1. **无限重定向**：检查公开页面配置是否正确
2. **Token验证失败**：检查后端API是否正常工作
3. **状态不同步**：确保使用了正确的认证store和组合式函数

### 调试方法

```javascript
// 查看当前认证状态
console.log('认证状态:', {
  isLoggedIn: authStore.isLoggedIn,
  user: authStore.user,
  token: authStore.token
})

// 手动清除认证状态
authStore.logout()
```
