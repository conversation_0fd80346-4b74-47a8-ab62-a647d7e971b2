package com.writing.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 提示词实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value = "prompts", autoResultMap = true)
public class Prompt {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    @TableField("user_id")
    private Long userId;
    
    @TableField("title")
    private String title;
    
    @TableField("category")
    private String category;
    
    @TableField("description")
    private String description;
    
    @TableField("content")
    private String content;
    
    @TableField(value = "tags", typeHandler = JacksonTypeHandler.class)
    private List<String> tags;
    
    @TableField("is_default")
    private Integer isDefault;
    
    @TableField("usage_count")
    private Integer usageCount;
    
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
    
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
    
    @TableLogic
    @TableField("deleted")
    private Integer deleted;
}
