package com.writing.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.writing.entity.Character;

import java.util.List;

/**
 * 人物服务接口
 */
public interface CharacterService extends IService<Character> {
    
    /**
     * 获取小说的人物列表（简化版，权限验证在Controller层）
     */
    List<Character> getCharactersByNovelId(Long novelId);

    /**
     * 获取人物详情（简化版，权限验证在Controller层）
     */
    Character getCharacterById(Long characterId, Long novelId);

    /**
     * 创建人物（简化版，权限验证在Controller层）
     */
    Character createCharacter(Character character);

    /**
     * 更新人物（简化版，权限验证在Controller层）
     */
    Character updateCharacter(Character character);

    /**
     * 删除人物（简化版，权限验证在Controller层）
     */
    boolean deleteCharacter(Long characterId, Long novelId);
}
