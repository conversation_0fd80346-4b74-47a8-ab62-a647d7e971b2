import apiClient from './apiClient'

/**
 * API配置相关API
 */
export const apiConfigApi = {
  
  /**
   * 获取用户的所有API配置
   */
  getApiConfigs() {
    return apiClient.get('/api-configs')
  },
  
  /**
   * 获取用户的默认API配置
   */
  getDefaultApiConfig() {
    return apiClient.get('/api-configs/default')
  },
  
  /**
   * 根据ID获取API配置详情
   */
  getApiConfig(id) {
    return apiClient.get(`/api-configs/${id}`)
  },
  
  /**
   * 创建API配置
   */
  createApiConfig(configData) {
    return apiClient.post('/api-configs', configData)
  },
  
  /**
   * 更新API配置
   */
  updateApiConfig(id, configData) {
    return apiClient.put(`/api-configs/${id}`, configData)
  },
  
  /**
   * 删除API配置
   */
  deleteApiConfig(id) {
    return apiClient.delete(`/api-configs/${id}`)
  },
  
  /**
   * 设置默认API配置
   */
  setDefaultApiConfig(id) {
    return apiClient.post(`/api-configs/${id}/set-default`)
  },
  
  /**
   * 复制API配置
   */
  duplicateApiConfig(id) {
    return apiClient.post(`/api-configs/${id}/duplicate`)
  },
  
  /**
   * 测试API配置连接
   */
  testApiConfig(id) {
    return apiClient.post(`/api-configs/${id}/test`)
  }
}

export default apiConfigApi
