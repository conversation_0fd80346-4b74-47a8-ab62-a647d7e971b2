-- 重置所有表的自增ID为1
-- 注意：执行前请确保已备份数据，此操作会清空所有表数据

SET FOREIGN_KEY_CHECKS = 0;

-- 清空所有表数据并重置自增ID
TRUNCATE TABLE `account_balance`;
TRUNCATE TABLE `announcement_reads`;
TRUNCATE TABLE `api_configs`;
TRUNCATE TABLE `billing_records`;
TRUNCATE TABLE `chapters`;
TRUNCATE TABLE `characters`;
TRUNCATE TABLE `corpus`;
TRUNCATE TABLE `events`;
TRUNCATE TABLE `novel_genres`;
TRUNCATE TABLE `novels`;
TRUNCATE TABLE `prompts`;
TRUNCATE TABLE `token_usage_stats`;
TRUNCATE TABLE `users`;
TRUNCATE TABLE `world_settings`;
TRUNCATE TABLE `writing_goals`;
TRUNCATE TABLE `writing_records`;

-- 或者，如果只想重置自增ID而保留数据，使用以下语句：
-- ALTER TABLE `account_balance` AUTO_INCREMENT = 1;
-- ALTER TABLE `announcement_reads` AUTO_INCREMENT = 1;
-- ALTER TABLE `api_configs` AUTO_INCREMENT = 1;
-- ALTER TABLE `billing_records` AUTO_INCREMENT = 1;
-- ALTER TABLE `chapters` AUTO_INCREMENT = 1;
-- ALTER TABLE `characters` AUTO_INCREMENT = 1;
-- ALTER TABLE `corpus` AUTO_INCREMENT = 1;
-- ALTER TABLE `events` AUTO_INCREMENT = 1;
-- ALTER TABLE `novel_genres` AUTO_INCREMENT = 1;
-- ALTER TABLE `novels` AUTO_INCREMENT = 1;
-- ALTER TABLE `prompts` AUTO_INCREMENT = 1;
-- ALTER TABLE `token_usage_stats` AUTO_INCREMENT = 1;
-- ALTER TABLE `users` AUTO_INCREMENT = 1;
-- ALTER TABLE `world_settings` AUTO_INCREMENT = 1;
-- ALTER TABLE `writing_goals` AUTO_INCREMENT = 1;
-- ALTER TABLE `writing_records` AUTO_INCREMENT = 1;

SET FOREIGN_KEY_CHECKS = 1;
