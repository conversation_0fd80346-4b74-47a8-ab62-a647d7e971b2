-- 动态生成重置所有自增ID的SQL语句
-- 执行此查询会生成所有需要的ALTER TABLE语句

SELECT 
    CONCAT('ALTER TABLE `', TABLE_NAME, '` AUTO_INCREMENT = 1;') AS reset_sql
FROM 
    INFORMATION_SCHEMA.TABLES 
WHERE 
    TABLE_SCHEMA = 'writing_db' 
    AND AUTO_INCREMENT IS NOT NULL
ORDER BY 
    TABLE_NAME;

-- 或者生成TRUNCATE语句（会清空数据）
SELECT 
    CONCAT('TRUNCATE TABLE `', TABLE_NAME, '`;') AS truncate_sql
FROM 
    INFORMATION_SCHEMA.TABLES 
WHERE 
    TABLE_SCHEMA = 'writing_db' 
    AND AUTO_INCREMENT IS NOT NULL
ORDER BY 
    TABLE_NAME;
