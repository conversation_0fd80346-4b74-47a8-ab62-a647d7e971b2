package com.writing.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.writing.entity.Novel;
import org.springframework.web.multipart.MultipartFile;

/**
 * 小说服务接口
 */
public interface NovelService extends IService<Novel> {
    
    /**
     * 分页查询用户的小说
     */
    IPage<Novel> getUserNovels(Long userId, int current, int size);
    
    /**
     * 获取用户的小说列表
     */
    java.util.List<Novel> getUserNovelList(Long userId);
    
    /**
     * 创建小说
     */
    Novel createNovel(Long userId, Novel novel);
    
    /**
     * 更新小说
     */
    Novel updateNovel(Long userId, Novel novel);
    
    /**
     * 删除小说
     */
    boolean deleteNovel(Long userId, Long novelId);
    
    /**
     * 获取小说详情
     */
    Novel getNovelDetail(Long userId, Long novelId);
    
    /**
     * 更新小说字数统计
     */
    void updateWordCount(Long novelId);

    /**
     * 上传小说文件
     */
    Novel uploadNovel(Long userId, MultipartFile file, String title, String genre,
                     String encoding, Boolean autoSplit, String description);
}
