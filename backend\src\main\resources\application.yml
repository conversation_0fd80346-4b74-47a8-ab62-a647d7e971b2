server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: writing-backend

  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **********************************************************************************************************************************************************
    username: writing_db
    password: 4b3HFpRmHzA5sJji

  redis:
    host: localhost
    port: 6379
    password:
    database: 0
    timeout: 10000ms
    lettuce:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0

  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8

  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB
      enabled: true

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: AUTO
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath*:/mapper/**/*.xml

# JWT配置
jwt:
  secret: writing-app-secret-key-2024-very-long-secure-key-for-jwt-hmac-sha-algorithm-minimum-256-bits
  expiration: 86400000 # 24小时

# 阿里云OSS配置
aliyun:
  oss:
    endpoint: oss-cn-guangzhou.aliyuncs.com
    access-key-id: LTAI5tBTPatgft654EEVzqUH
    access-key-secret: ******************************
    bucket-name: contract-review
    url-prefix: https://contract-review.oss-cn-guangzhou.aliyuncs.com/

# 文件上传配置
file:
  upload:
    max-size: 50MB
    allowed-types: jpg,jpeg,png,gif,bmp,webp,txt

# 日志配置
logging:
  level:
    com.writing: debug
    org.springframework.security: debug
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n'
