# 新增API端点测试说明

## 1. 章节管理API (ChapterController)

### 基础路径: `/api/novels/{novelId}/chapters`

#### 1.1 获取章节列表
```http
GET /api/novels/{novelId}/chapters
Authorization: Bearer {token}
```

#### 1.2 获取章节详情
```http
GET /api/novels/{novelId}/chapters/{chapterId}
Authorization: Bearer {token}
```

#### 1.3 创建章节
```http
POST /api/novels/{novelId}/chapters
Authorization: Bearer {token}
Content-Type: application/json

{
  "title": "第一章 开始",
  "content": "章节内容...",
  "summary": "章节摘要",
  "outline": "章节大纲",
  "notes": "章节备注",
  "status": "draft",
  "chapterOrder": 1
}
```

#### 1.4 更新章节
```http
PUT /api/novels/{novelId}/chapters/{chapterId}
Authorization: Bearer {token}
Content-Type: application/json

{
  "title": "第一章 开始（修改）",
  "content": "修改后的章节内容...",
  "status": "completed"
}
```

#### 1.5 删除章节
```http
DELETE /api/novels/{novelId}/chapters/{chapterId}
Authorization: Bearer {token}
```

#### 1.6 更新章节顺序
```http
PUT /api/novels/{novelId}/chapters/order
Authorization: Bearer {token}
Content-Type: application/json

[
  {"id": 1},
  {"id": 3},
  {"id": 2}
]
```

## 2. 人物管理API (CharacterController)

### 基础路径: `/api/novels/{novelId}/characters`

#### 2.1 获取人物列表
```http
GET /api/novels/{novelId}/characters
Authorization: Bearer {token}
```

#### 2.2 获取人物详情
```http
GET /api/novels/{novelId}/characters/{characterId}
Authorization: Bearer {token}
```

#### 2.3 创建人物
```http
POST /api/novels/{novelId}/characters
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "张三",
  "role": "protagonist",
  "gender": "male",
  "age": 25,
  "appearance": "外貌描述",
  "personality": "性格描述",
  "background": "背景故事",
  "tags": ["主角", "勇敢"],
  "avatar": "头像URL"
}
```

#### 2.4 更新人物
```http
PUT /api/novels/{novelId}/characters/{characterId}
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "张三（修改）",
  "age": 26,
  "personality": "修改后的性格描述"
}
```

#### 2.5 删除人物
```http
DELETE /api/novels/{novelId}/characters/{characterId}
Authorization: Bearer {token}
```

## 3. 提示词管理API (PromptController)

### 基础路径: `/api/prompts`

#### 3.1 获取提示词列表
```http
GET /api/prompts
Authorization: Bearer {token}

# 按分类筛选
GET /api/prompts?category=content
Authorization: Bearer {token}
```

#### 3.2 获取提示词详情
```http
GET /api/prompts/{promptId}
Authorization: Bearer {token}
```

#### 3.3 创建提示词
```http
POST /api/prompts
Authorization: Bearer {token}
Content-Type: application/json

{
  "title": "内容生成提示词",
  "category": "content",
  "description": "用于生成小说内容的提示词",
  "content": "请根据以下设定生成小说内容...",
  "tags": ["内容生成", "小说"]
}
```

#### 3.4 更新提示词
```http
PUT /api/prompts/{promptId}
Authorization: Bearer {token}
Content-Type: application/json

{
  "title": "修改后的提示词标题",
  "content": "修改后的提示词内容..."
}
```

#### 3.5 删除提示词
```http
DELETE /api/prompts/{promptId}
Authorization: Bearer {token}
```

#### 3.6 增加使用次数
```http
POST /api/prompts/{promptId}/use
Authorization: Bearer {token}
```

## 4. 测试流程建议

### 4.1 准备工作
1. 启动后端服务
2. 注册/登录获取JWT token
3. 创建一部小说获取novelId

### 4.2 测试章节管理
1. 创建几个章节
2. 获取章节列表验证
3. 更新章节内容
4. 调整章节顺序
5. 删除章节

### 4.3 测试人物管理
1. 为小说创建几个人物
2. 获取人物列表
3. 更新人物信息
4. 删除人物

### 4.4 测试提示词管理
1. 创建不同分类的提示词
2. 按分类获取提示词
3. 更新提示词
4. 增加使用次数
5. 删除提示词

## 5. 预期响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "success",
  "data": {
    // 具体数据
  }
}
```

### 错误响应
```json
{
  "code": 500,
  "message": "错误信息",
  "data": null
}
```

## 6. 注意事项

1. **认证**: 所有API都需要JWT token认证
2. **权限**: 用户只能操作自己的数据
3. **数据验证**: 必填字段不能为空
4. **错误处理**: 注意捕获和处理各种异常情况
5. **ID类型**: 现在使用自增ID，数值较小（1, 2, 3...）
