package com.writing.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.writing.entity.Prompt;

import java.util.List;

/**
 * 提示词服务接口
 */
public interface PromptService extends IService<Prompt> {
    
    /**
     * 获取用户的提示词列表（可按分类筛选）
     */
    List<Prompt> getPromptsByUserId(Long userId, String category);
    
    /**
     * 获取提示词详情（带用户权限验证）
     */
    Prompt getPromptById(Long promptId, Long userId);
    
    /**
     * 创建提示词
     */
    Prompt createPrompt(Prompt prompt);
    
    /**
     * 更新提示词（带用户权限验证）
     */
    Prompt updatePrompt(Prompt prompt, Long userId);
    
    /**
     * 删除提示词（带用户权限验证）
     */
    boolean deletePrompt(Long promptId, Long userId);
    
    /**
     * 增加提示词使用次数
     */
    void incrementUsageCount(Long promptId, Long userId);
    
    /**
     * 初始化用户的默认提示词
     */
    void initDefaultPrompts(Long userId);

    /**
     * 获取默认提示词列表
     */
    List<Prompt> getDefaultPrompts();
}
