# PromptSelector 提示词选择组件

一个独立的、可复用的提示词选择组件，用于在AI写作应用中选择和配置提示词模板。

## 功能特性

- 🎯 **分类筛选**：支持按类别筛选提示词（章节大纲、人物生成、世界观等）
- 🔧 **变量填充**：自动提取提示词中的变量并提供填充界面
- 📝 **实时预览**：实时生成最终提示词预览
- 📋 **一键复制**：支持复制最终提示词到剪贴板
- 🔄 **自动填充**：支持根据上下文自动填充变量
- 📖 **章节选择**：特殊支持前文概要的章节多选功能

## 组件结构

```
src/
├── components/
│   └── PromptSelector.vue          # 主组件
├── composables/
│   └── usePromptSelector.js        # 组合式函数
└── examples/
    └── PromptSelectorUsage.vue     # 使用示例
```

## 快速开始

### 1. 基础使用

```vue
<template>
  <div>
    <el-button @click="openPromptSelector">选择提示词</el-button>
    
    <PromptSelector
      v-model:visible="visible"
      :category="category"
      :prompts="prompts"
      :auto-fill-data="autoFillData"
      @confirm="handleConfirm"
      @cancel="handleCancel"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import PromptSelector from '@/components/PromptSelector.vue'

const visible = ref(false)
const category = ref('character')
const prompts = ref([
  {
    id: '1',
    title: '人物生成器',
    description: '生成小说人物',
    category: 'character',
    content: '请为小说《{小说标题}》生成一个{角色定位}角色...',
    tags: ['人物', '生成']
  }
])
const autoFillData = ref({
  '小说标题': '我的小说',
  '角色定位': '主角'
})

const openPromptSelector = () => {
  visible.value = true
}

const handleConfirm = (result) => {
  console.log('选择结果：', result)
  visible.value = false
}

const handleCancel = () => {
  visible.value = false
}
</script>
```

### 2. 使用组合式函数

```vue
<template>
  <div>
    <el-button @click="openCharacterPrompt">人物生成</el-button>
    
    <PromptSelector
      v-model:visible="promptSelector.visible.value"
      :category="promptSelector.category.value"
      :prompts="promptSelector.availablePrompts.value"
      :auto-fill-data="promptSelector.autoFillData.value"
      @confirm="handlePromptConfirm"
      @cancel="promptSelector.closeSelector"
    />
  </div>
</template>

<script setup>
import { usePromptSelector } from '@/composables/usePromptSelector'
import PromptSelector from '@/components/PromptSelector.vue'

const promptSelector = usePromptSelector()

const openCharacterPrompt = () => {
  const autoFillData = promptSelector.createAutoFillData.character(
    { title: '我的小说', genre: '现代' },
    { name: '张三', role: 'protagonist' }
  )

  promptSelector.openSelector({
    category: 'character',
    prompts: mockPrompts,
    autoFillData
  })
}

const handlePromptConfirm = (result) => {
  console.log('选择结果：', result)
  promptSelector.closeSelector()
  
  // 根据分类执行不同操作
  switch (result.category) {
    case 'character':
      generateCharacter(result.finalPrompt)
      break
    // ... 其他分类
  }
}
</script>
```

## API 文档

### PromptSelector 组件 Props

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| visible | Boolean | false | 对话框显示状态 |
| category | String | '' | 提示词分类 |
| prompts | Array | [] | 可用提示词列表 |
| autoFillData | Object | {} | 自动填充数据 |
| availableContextChapters | Array | [] | 可选择的上下文章节 |
| loading | Boolean | false | 加载状态 |
| title | String | '选择提示词' | 对话框标题 |

### PromptSelector 组件 Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:visible | Boolean | 更新显示状态 |
| confirm | Object | 确认选择，返回选择结果 |
| cancel | - | 取消选择 |
| goToPromptLibrary | - | 跳转到提示词库 |

### 选择结果对象结构

```javascript
{
  prompt: {           // 选中的提示词对象
    id: '1',
    title: '提示词标题',
    description: '提示词描述',
    category: 'character',
    content: '提示词内容...',
    tags: ['标签1', '标签2']
  },
  variables: {        // 填充的变量值
    '小说标题': '我的小说',
    '角色定位': '主角'
  },
  finalPrompt: '...',  // 最终生成的提示词
  contextChapters: [], // 选中的上下文章节ID
  category: 'character' // 提示词分类
}
```

### usePromptSelector 组合式函数

#### 返回的状态

| 属性名 | 类型 | 说明 |
|--------|------|------|
| visible | Ref\<Boolean\> | 对话框显示状态 |
| category | Ref\<String\> | 当前分类 |
| selectedPrompt | Ref\<Object\> | 选中的提示词 |
| variables | Ref\<Object\> | 变量值 |
| finalPrompt | Ref\<String\> | 最终提示词 |
| loading | Ref\<Boolean\> | 加载状态 |

#### 返回的方法

| 方法名 | 参数 | 说明 |
|--------|------|------|
| openSelector | config: Object | 打开选择器 |
| closeSelector | - | 关闭选择器 |
| selectPrompt | prompt: Object | 选择提示词 |
| generateFinalPrompt | - | 生成最终提示词 |
| getSelectionResult | - | 获取选择结果 |
| copyPromptToClipboard | - | 复制提示词到剪贴板 |

#### 预设自动填充方法

```javascript
// 人物生成自动填充
const autoFillData = promptSelector.createAutoFillData.character(
  novelInfo,      // 小说信息
  characterForm   // 人物表单数据
)

// 章节生成自动填充
const autoFillData = promptSelector.createAutoFillData.chapter(
  novelInfo,      // 小说信息
  chapterInfo,    // 章节信息
  config          // 生成配置
)

// 世界观生成自动填充
const autoFillData = promptSelector.createAutoFillData.worldview(
  novelInfo,      // 小说信息
  config          // 生成配置
)

// 文本优化自动填充
const autoFillData = promptSelector.createAutoFillData.optimize(
  content,        // 原始内容
  novelInfo       // 小说信息
)
```

## 支持的提示词分类

| 分类代码 | 显示名称 | 说明 |
|----------|----------|------|
| outline | 章节大纲 | 用于生成章节大纲 |
| content | 基础正文 | 用于生成章节正文 |
| content-dialogue | 对话生成 | 专门用于对话生成 |
| content-scene | 场景描写 | 专门用于场景描写 |
| content-action | 动作描写 | 专门用于动作描写 |
| character | 人物生成 | 用于生成人物角色 |
| worldview | 世界观生成 | 用于生成世界观设定 |
| optimize | 文本优化 | 用于优化现有文本 |
| continue | 续写生成 | 用于续写内容 |

## 提示词模板格式

提示词内容中使用 `{变量名}` 格式定义变量：

```
请为小说《{小说标题}》生成一个{角色定位}角色。

基本信息：
- 姓名：{姓名}
- 性别：{性别}
- 年龄：{年龄}

请详细描述该角色的外貌、性格、背景故事等。
```

## 特殊功能

### 前文概要章节选择

当提示词包含 `{前文概要}` 变量时，会自动显示章节多选框，用户可以选择相关章节作为前文参考。

### 自动填充逻辑

组件会根据传入的 `autoFillData` 自动填充对应的变量，减少用户手动输入。

### 实时预览

用户修改变量值时，最终提示词会实时更新，方便用户预览效果。

## 最佳实践

1. **分类管理**：为不同用途的提示词设置合适的分类
2. **变量命名**：使用清晰、一致的变量命名规范
3. **自动填充**：充分利用自动填充功能提升用户体验
4. **错误处理**：在使用组件时添加适当的错误处理逻辑
5. **状态管理**：使用组合式函数统一管理提示词相关状态

## 扩展开发

如需添加新的提示词分类或自动填充逻辑，可以：

1. 在 `categoryNames` 中添加新分类
2. 在 `createAutoFillData` 中添加新的自动填充方法
3. 根据需要扩展组件的特殊处理逻辑
