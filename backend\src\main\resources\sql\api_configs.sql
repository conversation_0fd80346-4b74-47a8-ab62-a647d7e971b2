-- API配置表
CREATE TABLE IF NOT EXISTS `api_configs` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `name` varchar(100) NOT NULL COMMENT '配置名称',
  `type` varchar(50) NOT NULL DEFAULT 'custom' COMMENT '配置类型',
  `description` text COMMENT '配置描述',
  `api_key` varchar(500) NOT NULL COMMENT 'API密钥',
  `base_url` varchar(500) NOT NULL COMMENT 'API基础地址',
  `selected_model` varchar(100) COMMENT '选择的模型',
  `max_tokens` int COMMENT '最大Token数',
  `unlimited_tokens` tinyint(1) DEFAULT 0 COMMENT '是否无限制Token',
  `temperature` decimal(3,2) DEFAULT 0.70 COMMENT '创造性参数',
  `top_p` decimal(3,2) DEFAULT 1.00 COMMENT 'Top P参数',
  `frequency_penalty` decimal(3,2) DEFAULT 0.00 COMMENT '频率惩罚',
  `presence_penalty` decimal(3,2) DEFAULT 0.00 COMMENT '存在惩罚',
  `timeout` int DEFAULT 30 COMMENT '超时时间(秒)',
  `stream_mode` tinyint(1) DEFAULT 1 COMMENT '是否启用流式模式',
  `retry_count` int DEFAULT 3 COMMENT '重试次数',
  `custom_headers` text COMMENT '自定义请求头',
  `status` varchar(20) DEFAULT 'disconnected' COMMENT '连接状态',
  `is_default` tinyint(1) DEFAULT 0 COMMENT '是否为默认配置',
  `enabled` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_user_default` (`user_id`, `is_default`),
  KEY `idx_user_enabled` (`user_id`, `enabled`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='API配置表';

-- 插入一些示例数据（可选）
INSERT INTO `api_configs` (`user_id`, `name`, `type`, `description`, `api_key`, `base_url`, `selected_model`, `max_tokens`, `unlimited_tokens`, `temperature`, `is_default`, `enabled`) VALUES
(1, 'OpenAI官方', 'openai', 'OpenAI官方API配置', 'your-openai-api-key', 'https://api.openai.com/v1', 'gpt-3.5-turbo', 2000000, 0, 0.70, 1, 1),
(1, 'DeepSeek配置', 'deepseek', 'DeepSeek API配置', 'your-deepseek-api-key', 'https://api.deepseek.com/v1', 'deepseek-chat', NULL, 1, 0.70, 0, 1);
