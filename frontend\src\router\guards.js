import { authUtils } from '@/services/authApi'
import { useAuthStore } from '@/stores/authStore'
import { ElMessage } from 'element-plus'

/**
 * 路由守卫
 */
export function setupRouterGuards(router) {

  // 全局前置守卫
  router.beforeEach(async (to, from, next) => {

    // 不需要认证的页面
    const publicPages = ['/login', '/register']
    const isPublicPage = publicPages.includes(to.path)

    if (isPublicPage) {
      // 如果是公开页面
      const authStore = useAuthStore()
      if (authStore.isLoggedIn && to.path === '/login') {
        // 已登录用户访问登录页，重定向到首页
        next('/')
      } else {
        next()
      }
    } else {
      // 需要认证的页面
      const authStore = useAuthStore()

      if (!authStore.isLoggedIn) {
        // 未登录，跳转到登录页
        ElMessage.warning('请先登录')
        next('/login')
        return
      }

      // 已登录，检查认证状态
      const isValid = authStore.checkAuthStatus()
      if (isValid) {
        // 在特定条件下验证token有效性
        if (shouldValidateToken(to, from)) {
          try {
            const tokenValid = await authStore.validateAuth()
            if (tokenValid) {
              next()
            } else {
              ElMessage.warning('登录已过期，请重新登录')
              next('/login')
            }
          } catch (error) {
            console.error('Token验证失败:', error)
            ElMessage.warning('登录验证失败，请重新登录')
            next('/login')
          }
        } else {
          next()
        }
      } else {
        // 认证状态检查失败，跳转到登录页
        next('/login')
      }
    }
  })

  /**
   * 判断是否需要验证token有效性
   * 避免每次路由都调用API，只在特定情况下验证
   */
  function shouldValidateToken(to, from) {
    // 如果是从登录页跳转过来，不需要验证（刚登录）
    if (from.path === '/login') {
      return false
    }

    // 如果上次验证时间距离现在超过5分钟，需要重新验证
    const lastValidateTime = sessionStorage.getItem('lastTokenValidateTime')
    if (!lastValidateTime) {
      sessionStorage.setItem('lastTokenValidateTime', Date.now().toString())
      return true
    }

    const now = Date.now()
    const lastTime = parseInt(lastValidateTime)
    const fiveMinutes = 5 * 60 * 1000

    if (now - lastTime > fiveMinutes) {
      sessionStorage.setItem('lastTokenValidateTime', now.toString())
      return true
    }

    return false
  }
  
  // 全局后置钩子
  router.afterEach((to, from) => {
    // 设置页面标题
    if (to.meta.title) {
      document.title = `${to.meta.title} - 写作应用`
    } else {
      document.title = '写作应用'
    }
  })
}
