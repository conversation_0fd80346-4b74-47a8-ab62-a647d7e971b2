package com.writing.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 写作目标实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value = "writing_goals", autoResultMap = true)
public class WritingGoal {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("user_id")
    private Long userId;

    @TableField("title")
    private String title;

    @TableField("description")
    private String description;

    @TableField("type")
    private String type;

    @TableField("target_value")
    private Integer targetValue;

    @TableField("current_value")
    private Integer currentValue;

    @TableField("unit")
    private String unit;

    @TableField("start_date")
    private LocalDate startDate;

    @TableField("end_date")
    private LocalDate endDate;

    @TableField("status")
    private String status;

    @TableField("priority")
    private Integer priority;

    @TableField("reminder")
    private Boolean reminder;

    @TableField("reminder_time")
    private String reminderTime;

    @TableField(value = "progress_history", typeHandler = JacksonTypeHandler.class)
    private List<Map<String, Object>> progressHistory;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    @TableLogic
    @TableField("deleted")
    private Integer deleted;
}
