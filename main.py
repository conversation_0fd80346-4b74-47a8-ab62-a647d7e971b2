#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小说章节自动切分工具主程序
提供命令行接口和示例使用
"""

import argparse
import json
import os
import sys
from pathlib import Path
from chapter_splitter import ChapterSplitter, NovelSplitResult
from chapter_validator import <PERSON>Valida<PERSON>
from typing import Optional


def read_text_file(file_path: str, encoding: str = 'utf-8') -> str:
    """
    读取文本文件
    
    Args:
        file_path: 文件路径
        encoding: 文件编码
        
    Returns:
        str: 文件内容
    """
    try:
        with open(file_path, 'r', encoding=encoding) as f:
            return f.read()
    except UnicodeDecodeError:
        # 如果UTF-8失败，尝试GBK
        if encoding == 'utf-8':
            print(f"UTF-8编码失败，尝试GBK编码...")
            return read_text_file(file_path, 'gbk')
        else:
            raise


def save_result_to_file(result: NovelSplitResult, output_path: str, 
                       include_content: bool = True) -> None:
    """
    保存结果到文件
    
    Args:
        result: 切分结果
        output_path: 输出文件路径
        include_content: 是否包含章节内容
    """
    splitter = ChapterSplitter()
    json_content = splitter.export_to_json(result, include_content)
    
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(json_content)
    
    print(f"结果已保存到: {output_path}")


def print_summary(result: NovelSplitResult, validation_result=None) -> None:
    """打印结果摘要"""
    print("\n" + "="*60)
    print(f"小说标题: {result.title}")
    print(f"总章节数: {result.total_chapters}")
    print(f"总字数: {result.total_words:,}")
    print(f"切分方法: {result.split_method}")
    print(f"置信度: {result.confidence:.2f}")
    
    if result.warnings:
        print(f"\n警告信息:")
        for warning in result.warnings:
            print(f"  - {warning}")
    
    print(f"\n章节概览:")
    for i, chapter in enumerate(result.chapters[:10]):  # 只显示前10章
        print(f"  第{chapter.chapter_number}章: {chapter.title[:30]}... "
              f"({chapter.word_count:,}字)")
    
    if len(result.chapters) > 10:
        print(f"  ... 还有{len(result.chapters) - 10}章")
    
    if validation_result:
        print(f"\n验证结果:")
        print(f"  总体评分: {validation_result.overall_score}/100")
        if validation_result.issues:
            print(f"  发现问题: {len(validation_result.issues)}个")
            for issue in validation_result.issues[:5]:  # 只显示前5个问题
                print(f"    - {issue}")
        
        if validation_result.suggestions:
            print(f"  改进建议:")
            for suggestion in validation_result.suggestions[:3]:  # 只显示前3个建议
                print(f"    - {suggestion}")
    
    print("="*60)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='小说章节自动切分工具')
    parser.add_argument('input_file', help='输入的小说文本文件路径')
    parser.add_argument('-o', '--output', help='输出JSON文件路径')
    parser.add_argument('-t', '--title', help='小说标题（可选）')
    parser.add_argument('-e', '--encoding', default='utf-8', 
                       help='文件编码（默认: utf-8）')
    parser.add_argument('--no-content', action='store_true',
                       help='输出结果不包含章节内容')
    parser.add_argument('--validate', action='store_true',
                       help='验证切分结果质量')
    parser.add_argument('--verbose', action='store_true',
                       help='显示详细信息')
    
    args = parser.parse_args()
    
    # 检查输入文件
    if not os.path.exists(args.input_file):
        print(f"错误: 文件 '{args.input_file}' 不存在")
        sys.exit(1)
    
    try:
        # 读取文件
        print(f"正在读取文件: {args.input_file}")
        text = read_text_file(args.input_file, args.encoding)
        
        if not text.strip():
            print("错误: 文件内容为空")
            sys.exit(1)
        
        print(f"文件读取成功，共 {len(text):,} 个字符")
        
        # 确定小说标题
        novel_title = args.title or Path(args.input_file).stem
        
        # 执行章节切分
        print("正在进行章节切分...")
        splitter = ChapterSplitter()
        result = splitter.split_novel(text, novel_title)
        
        # 验证结果（如果需要）
        validation_result = None
        if args.validate:
            print("正在验证切分结果...")
            validator = ChapterValidator()
            validation_result = validator.validate(result)
        
        # 打印摘要
        print_summary(result, validation_result)
        
        # 保存结果
        if args.output:
            save_result_to_file(result, args.output, not args.no_content)
        else:
            # 默认输出文件名
            output_path = Path(args.input_file).with_suffix('.json')
            save_result_to_file(result, str(output_path), not args.no_content)
        
        # 详细信息
        if args.verbose and validation_result:
            print(f"\n详细统计信息:")
            for key, value in validation_result.statistics.items():
                if isinstance(value, float):
                    print(f"  {key}: {value:.2f}")
                else:
                    print(f"  {key}: {value}")
            
            print(f"\n质量指标:")
            for key, value in validation_result.quality_metrics.items():
                if isinstance(value, float):
                    print(f"  {key}: {value:.2f}")
                else:
                    print(f"  {key}: {value}")
        
        print("\n处理完成！")
        
    except Exception as e:
        print(f"错误: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


def demo():
    """演示函数"""
    print("小说章节切分工具演示")
    print("="*40)
    
    # 创建示例文本
    sample_text = """
    第一章 开始的故事
    
    这是第一章的内容。在一个遥远的地方，有一个美丽的村庄。
    村庄里住着许多善良的人们，他们过着平静而幸福的生活。
    
    第二章 冒险的开始
    
    第二天，主人公踏上了冒险的旅程。他背着行囊，怀着忐忑不安的心情，
    走出了村庄的大门。前方的路充满了未知和挑战。
    
    第三章 遇到困难
    
    在旅途中，主人公遇到了许多困难。有时是恶劣的天气，
    有时是危险的野兽，但他都勇敢地克服了。
    
    第四章 获得帮助
    
    就在主人公感到绝望的时候，一位神秘的老人出现了。
    老人给了他一把神奇的剑，并告诉他如何使用。
    
    第五章 最终胜利
    
    凭借着勇气和智慧，主人公最终战胜了所有的困难，
    完成了他的使命，成为了真正的英雄。
    """
    
    # 执行切分
    splitter = ChapterSplitter()
    result = splitter.split_novel(sample_text, "示例小说")
    
    # 验证结果
    validator = ChapterValidator()
    validation_result = validator.validate(result)
    
    # 显示结果
    print_summary(result, validation_result)
    
    # 保存示例结果
    save_result_to_file(result, "demo_result.json", True)
    
    print("\n演示完成！结果已保存到 demo_result.json")


if __name__ == "__main__":
    if len(sys.argv) == 1:
        # 如果没有参数，运行演示
        demo()
    else:
        # 运行主程序
        main()
