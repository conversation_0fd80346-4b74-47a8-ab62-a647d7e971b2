-- 重置所有表的自增ID为1（保留现有数据）
-- 注意：这会将自增ID重置为当前最大ID+1

-- 重置自增ID为1（如果表为空）或最大ID+1（如果表有数据）
ALTER TABLE `account_balance` AUTO_INCREMENT = 1;
ALTER TABLE `announcement_reads` AUTO_INCREMENT = 1;
ALTER TABLE `api_configs` AUTO_INCREMENT = 1;
ALTER TABLE `billing_records` AUTO_INCREMENT = 1;
ALTER TABLE `chapters` AUTO_INCREMENT = 1;
ALTER TABLE `characters` AUTO_INCREMENT = 1;
ALTER TABLE `corpus` AUTO_INCREMENT = 1;
ALTER TABLE `events` AUTO_INCREMENT = 1;
ALTER TABLE `novel_genres` AUTO_INCREMENT = 1;
ALTER TABLE `novels` AUTO_INCREMENT = 1;
ALTER TABLE `prompts` AUTO_INCREMENT = 1;
ALTER TABLE `token_usage_stats` AUTO_INCREMENT = 1;
ALTER TABLE `users` AUTO_INCREMENT = 1;
ALTER TABLE `world_settings` AUTO_INCREMENT = 1;
ALTER TABLE `writing_goals` AUTO_INCREMENT = 1;
ALTER TABLE `writing_records` AUTO_INCREMENT = 1;
