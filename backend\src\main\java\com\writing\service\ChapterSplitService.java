package com.writing.service;

import com.writing.entity.Novel;
import org.springframework.web.multipart.MultipartFile;

/**
 * 章节拆分服务接口
 */
public interface ChapterSplitService {
    
    /**
     * 拆分小说章节
     * 
     * @param novel 小说对象
     * @param file 小说文件
     * @param encoding 文件编码
     * @return 拆分结果
     */
    ChapterSplitResult splitChapters(Novel novel, MultipartFile file, String encoding);
    
    /**
     * 异步拆分小说章节
     *
     * @param novelId 小说ID
     * @param content 小说内容
     */
    void splitChaptersAsync(Long novelId, String content);
    
    /**
     * 章节拆分结果
     */
    class ChapterSplitResult {
        private boolean success;
        private String message;
        private int chapterCount;
        private int totalWords;
        
        public ChapterSplitResult(boolean success, String message, int chapterCount, int totalWords) {
            this.success = success;
            this.message = message;
            this.chapterCount = chapterCount;
            this.totalWords = totalWords;
        }
        
        // Getters and setters
        public boolean isSuccess() {
            return success;
        }
        
        public void setSuccess(boolean success) {
            this.success = success;
        }
        
        public String getMessage() {
            return message;
        }
        
        public void setMessage(String message) {
            this.message = message;
        }
        
        public int getChapterCount() {
            return chapterCount;
        }
        
        public void setChapterCount(int chapterCount) {
            this.chapterCount = chapterCount;
        }
        
        public int getTotalWords() {
            return totalWords;
        }
        
        public void setTotalWords(int totalWords) {
            this.totalWords = totalWords;
        }
    }
}
