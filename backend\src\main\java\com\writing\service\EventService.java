package com.writing.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.writing.entity.Event;

import java.util.List;

/**
 * 事件Service接口
 */
public interface EventService extends IService<Event> {

    /**
     * 获取小说的事件列表
     */
    List<Event> getEventsByNovelId(Long novelId);

    /**
     * 根据ID获取事件
     */
    Event getEventById(Long eventId, Long novelId);

    /**
     * 创建事件
     */
    Event createEvent(Event event);

    /**
     * 更新事件
     */
    Event updateEvent(Event event);

    /**
     * 删除事件
     */
    boolean deleteEvent(Long eventId, Long novelId);

    /**
     * 根据重要性获取事件列表
     */
    List<Event> getEventsByImportance(Long novelId, String importance);

    /**
     * 根据章节获取事件列表
     */
    List<Event> getEventsByChapter(Long novelId, String chapter);
}
