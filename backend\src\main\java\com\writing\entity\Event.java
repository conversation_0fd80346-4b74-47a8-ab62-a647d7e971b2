package com.writing.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 事件实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("events")
public class Event {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    @TableField("novel_id")
    private Long novelId;
    
    @TableField("title")
    private String title;
    
    @TableField("description")
    private String description;
    
    @TableField("chapter")
    private String chapter;
    
    @TableField("time")
    private String time;
    
    @TableField("importance")
    private String importance;
    
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
    
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
    
    @TableLogic
    @TableField("deleted")
    private Integer deleted;
}
