import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'

/**
 * 提示词选择器组合式函数
 * @param {Object} options 配置选项
 * @returns {Object} 提示词选择器相关的状态和方法
 */
export function usePromptSelector(options = {}) {
  // 基础状态
  const visible = ref(false)
  const category = ref('')
  const selectedPrompt = ref(null)
  const variables = ref({})
  const finalPrompt = ref('')
  const loading = ref(false)
  
  // 上下文章节选择
  const contextChapters = ref([])
  
  // 可用提示词列表
  const availablePrompts = ref([])
  
  // 自动填充数据
  const autoFillData = ref({})
  
  // 可用的上下文章节
  const availableContextChapters = ref([])

  // 计算属性
  const filteredPrompts = computed(() => {
    return availablePrompts.value.filter(prompt => prompt.category === category.value)
  })

  // 分类显示名称映射
  const categoryNames = {
    outline: '章节大纲',
    content: '基础正文',
    'content-dialogue': '对话生成',
    'content-scene': '场景描写',
    'content-action': '动作描写',
    character: '人物生成',
    worldview: '世界观生成',
    optimize: '文本优化',
    continue: '续写生成'
  }

  const getCategoryDisplayName = () => {
    return categoryNames[category.value] || '提示词'
  }

  // 核心方法
  const openSelector = (config = {}) => {
    category.value = config.category || ''
    availablePrompts.value = config.prompts || []
    autoFillData.value = config.autoFillData || {}
    availableContextChapters.value = config.availableContextChapters || []
    
    visible.value = true
    
    // 重置状态
    selectedPrompt.value = null
    variables.value = {}
    finalPrompt.value = ''
    contextChapters.value = []
  }

  const closeSelector = () => {
    visible.value = false
    // 重置状态
    selectedPrompt.value = null
    variables.value = {}
    finalPrompt.value = ''
    contextChapters.value = []
    category.value = ''
    autoFillData.value = {}
  }

  const selectPrompt = (prompt) => {
    selectedPrompt.value = prompt
    variables.value = {}

    // 提取变量
    const matches = prompt.content.match(/\{([^}]+)\}/g)
    if (matches) {
      matches.forEach(match => {
        const variable = match.slice(1, -1)
        variables.value[variable] = ''
      })
    }

    // 自动填充变量
    autoFillVariables()
    generateFinalPrompt()
  }

  const autoFillVariables = () => {
    if (!selectedPrompt.value || !autoFillData.value) return

    // 根据传入的自动填充数据填充变量
    Object.keys(variables.value).forEach(variable => {
      if (autoFillData.value[variable]) {
        variables.value[variable] = autoFillData.value[variable]
      }
    })
  }

  const generateFinalPrompt = () => {
    if (!selectedPrompt.value) {
      finalPrompt.value = ''
      return
    }

    let result = selectedPrompt.value.content
    Object.keys(variables.value).forEach(variable => {
      const value = variables.value[variable] || `{${variable}}`
      result = result.replace(new RegExp(`\\{${variable}\\}`, 'g'), value)
    })

    finalPrompt.value = result
  }

  const updateContextVariable = () => {
    if (!contextChapters.value.length) {
      variables.value['前文概要'] = ''
      generateFinalPrompt()
      return
    }

    // 根据选中的章节生成前文概要
    const selectedChapters = availableContextChapters.value.filter(
      chapter => contextChapters.value.includes(chapter.id)
    )

    const chaptersInfo = selectedChapters.map(chapter => {
      const cleanContent = chapter.content ? 
        chapter.content.replace(/<[^>]*>/g, '').replace(/&nbsp;/g, ' ').trim() : ''
      
      return `第${chapter.chapterIndex}章 ${chapter.title}：
大纲：${chapter.description || '暂无大纲'}
${cleanContent ? `内容概要：${cleanContent.substring(0, 200)}...` : ''}`
    }).join('\n\n')

    variables.value['前文概要'] = chaptersInfo
    generateFinalPrompt()
  }

  const clearContextSelection = () => {
    contextChapters.value = []
    variables.value['前文概要'] = ''
    generateFinalPrompt()
  }

  const copyPromptToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(finalPrompt.value)
      ElMessage.success('提示词已复制到剪贴板')
    } catch (error) {
      ElMessage.error('复制失败，请手动复制')
    }
  }

  const validateSelection = () => {
    if (!selectedPrompt.value) {
      ElMessage.warning('请先选择提示词')
      return false
    }
    
    if (!finalPrompt.value.trim()) {
      ElMessage.warning('请填充必要的变量')
      return false
    }
    
    return true
  }

  const getSelectionResult = () => {
    if (!validateSelection()) {
      return null
    }

    return {
      prompt: selectedPrompt.value,
      variables: { ...variables.value },
      finalPrompt: finalPrompt.value,
      contextChapters: [...contextChapters.value],
      category: category.value
    }
  }

  // 预设的自动填充函数
  const createAutoFillData = {
    // 人物生成自动填充
    character: (novelInfo, characterForm) => ({
      '小说标题': novelInfo?.title || '未命名小说',
      '姓名': characterForm?.name || '',
      '性别': characterForm?.gender === 'male' ? '男' : 
              characterForm?.gender === 'female' ? '女' : '其他',
      '年龄': characterForm?.age?.toString() || '25',
      '角色定位': getRoleText(characterForm?.role),
      '小说类型': novelInfo?.genre || '现代'
    }),

    // 章节生成自动填充
    chapter: (novelInfo, chapterInfo, config = {}) => ({
      '小说标题': novelInfo?.title || '未命名小说',
      '章节标题': chapterInfo?.title || '',
      '章节大纲': chapterInfo?.description || '暂无大纲',
      '目标字数': config.wordCount?.toString() || '2000',
      '写作视角': getViewpointDescription(config.style) || '第三人称',
      '重点内容': config.focus || '按大纲发展',
      '小说类型': novelInfo?.genre || '现代'
    }),

    // 世界观生成自动填充
    worldview: (novelInfo, config = {}) => ({
      '小说标题': novelInfo?.title || '未命名小说',
      '小说类型': novelInfo?.genre || '现代',
      '小说简介': novelInfo?.description || '暂无简介',
      '生成数量': config.count?.toString() || '3',
      '特殊要求': config.customPrompt || '符合小说世界观设定'
    }),

    // 优化生成自动填充
    optimize: (content, novelInfo) => ({
      '原始文本': content || '',
      '小说类型': novelInfo?.genre || '通用',
      '优化目标': '提升文本质量和可读性'
    })
  }

  // 辅助函数
  const getRoleText = (role) => {
    const roleMapping = {
      protagonist: '主角',
      supporting: '配角',
      antagonist: '反派',
      minor: '次要角色'
    }
    return roleMapping[role] || '配角'
  }

  const getViewpointDescription = (style) => {
    const styleMapping = {
      'first-person': '第一人称',
      'third-person': '第三人称',
      'omniscient': '全知视角'
    }
    return styleMapping[style] || '第三人称'
  }

  // 监听变量变化
  watch(variables, () => {
    generateFinalPrompt()
  }, { deep: true })

  return {
    // 状态
    visible,
    category,
    selectedPrompt,
    variables,
    finalPrompt,
    loading,
    contextChapters,
    availablePrompts,
    autoFillData,
    availableContextChapters,
    
    // 计算属性
    filteredPrompts,
    
    // 方法
    openSelector,
    closeSelector,
    selectPrompt,
    autoFillVariables,
    generateFinalPrompt,
    updateContextVariable,
    clearContextSelection,
    copyPromptToClipboard,
    validateSelection,
    getSelectionResult,
    getCategoryDisplayName,
    
    // 预设自动填充
    createAutoFillData
  }
}

// 默认导出
export default usePromptSelector