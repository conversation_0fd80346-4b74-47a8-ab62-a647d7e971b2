package com.writing.service;

import com.writing.entity.ApiConfig;
import java.util.List;

/**
 * API配置服务接口
 */
public interface ApiConfigService {

    /**
     * 根据用户ID获取所有API配置
     */
    List<ApiConfig> getConfigsByUserId(Long userId);

    /**
     * 根据用户ID获取默认API配置
     */
    ApiConfig getDefaultConfigByUserId(Long userId);

    /**
     * 根据ID获取API配置
     */
    ApiConfig getConfigById(Long id);

    /**
     * 创建API配置
     */
    ApiConfig createConfig(ApiConfig apiConfig);

    /**
     * 更新API配置
     */
    ApiConfig updateConfig(ApiConfig apiConfig);

    /**
     * 删除API配置
     */
    void deleteConfig(Long id);

    /**
     * 设置默认配置
     */
    void setDefaultConfig(Long userId, Long configId);

    /**
     * 复制配置
     */
    ApiConfig duplicateConfig(Long configId);

    /**
     * 测试API配置连接
     */
    boolean testConnection(ApiConfig config);
}
