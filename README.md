# 写作应用 - 前后端分离版本

这是一个基于Vue.js + Java Spring Boot的写作应用，实现了前后端分离架构。

## 技术栈

### 后端
- Java 17
- Spring Boot 3.2.0
- Spring Security
- MySQL 8.0
- MyBatis-Plus 3.5.4
- JWT认证
- Maven

### 前端
- Vue.js 3
- Pinia状态管理
- Element Plus UI组件库
- Axios HTTP客户端
- Vite构建工具

## 项目结构

```
├── backend/                 # Java后端项目
│   ├── src/main/java/
│   │   └── com/writing/
│   │       ├── controller/  # 控制器层
│   │       ├── service/     # 业务逻辑层
│   │       ├── entity/      # 实体类
│   │       ├── mapper/      # 数据访问层
│   │       ├── config/      # 配置类
│   │       ├── filter/      # 过滤器
│   │       ├── util/        # 工具类
│   │       └── common/      # 通用类
│   └── src/main/resources/
│       ├── application.yml  # 应用配置
│       └── db/migration/    # 数据库脚本
├── frontend/                # Vue前端项目
│   ├── src/
│   │   ├── components/      # 组件
│   │   ├── views/          # 页面
│   │   ├── stores/         # Pinia状态管理
│   │   ├── services/       # API服务
│   │   └── router/         # 路由配置
│   └── package.json
└── README.md
```

## 数据库设计

### 主要数据表
- `users` - 用户表
- `novels` - 小说表
- `chapters` - 章节表
- `characters` - 人物表
- `world_settings` - 世界观设定表
- `corpus` - 语料库表
- `events` - 事件表
- `prompts` - 提示词表
- `writing_goals` - 写作目标表
- `novel_genres` - 小说类型表
- `api_configs` - API配置表
- `billing_records` - 计费记录表

## 部署指南

### 1. 环境准备

#### 后端环境
- JDK 17+
- Maven 3.6+
- MySQL 8.0+
- Redis (可选)

#### 前端环境
- Node.js 16+
- npm 或 yarn

### 2. 数据库初始化

1. 创建MySQL数据库：
```sql
CREATE DATABASE writing_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 执行数据库脚本：
```bash
mysql -u root -p writing_db < backend/src/main/resources/db/migration/V1__init_database.sql
```

### 3. 后端部署

1. 修改配置文件 `backend/src/main/resources/application.yml`：
```yaml
spring:
  datasource:
    url: ***********************************************************************************************************************
    username: your_username
    password: your_password
```

2. 编译和运行：
```bash
cd backend
mvn clean package
java -jar target/writing-backend-1.0.0.jar
```

或者使用Maven直接运行：
```bash
cd backend
mvn spring-boot:run
```

后端服务将在 `http://localhost:8080` 启动。

### 4. 前端部署

1. 安装依赖：
```bash
cd frontend
npm install
```

2. 修改API基础URL（如果需要）：
编辑 `frontend/src/services/apiClient.js`：
```javascript
const apiClient = axios.create({
  baseURL: 'http://localhost:8080/api', // 修改为你的后端地址
  // ...
})
```

3. 开发模式运行：
```bash
npm run dev
```

4. 生产构建：
```bash
npm run build
```

## API接口文档

### 认证接口
- `POST /api/auth/register` - 用户注册
- `POST /api/auth/login` - 用户登录
- `GET /api/auth/me` - 获取当前用户信息

### 小说接口
- `GET /api/novels` - 获取小说列表（分页）
- `GET /api/novels/list` - 获取所有小说列表
- `GET /api/novels/{id}` - 获取小说详情
- `POST /api/novels` - 创建小说
- `PUT /api/novels/{id}` - 更新小说
- `DELETE /api/novels/{id}` - 删除小说

### 章节接口
- `GET /api/novels/{novelId}/chapters` - 获取章节列表
- `POST /api/novels/{novelId}/chapters` - 创建章节
- `PUT /api/novels/{novelId}/chapters/{id}` - 更新章节
- `DELETE /api/novels/{novelId}/chapters/{id}` - 删除章节

## 前端迁移指南

### 1. 替换localStorage操作

原来的localStorage操作：
```javascript
// 旧代码
const novels = JSON.parse(localStorage.getItem('novels') || '[]')
localStorage.setItem('novels', JSON.stringify(novels))
```

替换为API调用：
```javascript
// 新代码
import { novelApi } from '@/services/novelApi'

// 获取数据
const novels = await novelApi.getNovelList()

// 保存数据
await novelApi.createNovel(novelData)
```

### 2. 更新Pinia Store

使用新的store结构，集成API调用：
```javascript
import { useNovelStore } from '@/stores/novelStore'

const novelStore = useNovelStore()
await novelStore.fetchNovels()
```

### 3. 添加认证

在需要认证的页面添加登录检查：
```javascript
import { authUtils } from '@/services/authApi'

// 检查登录状态
if (!authUtils.isLoggedIn()) {
  router.push('/login')
}
```

## 注意事项

1. **CORS配置**：确保后端正确配置了CORS，允许前端域名访问。

2. **JWT Token**：前端会自动在请求头中添加JWT token，确保后端正确验证。

3. **错误处理**：API调用已经集成了统一的错误处理，会自动显示错误消息。

4. **数据迁移**：如果有现有的localStorage数据，需要编写迁移脚本将数据导入到数据库中。

## 开发建议

1. **API优先**：先设计和实现API接口，再修改前端调用。

2. **渐进式迁移**：可以逐步将localStorage操作替换为API调用，不需要一次性全部替换。

3. **测试**：每个API接口都应该进行充分测试，确保数据的正确性和完整性。

4. **性能优化**：考虑添加缓存机制，减少不必要的API调用。

## 许可证

MIT License
