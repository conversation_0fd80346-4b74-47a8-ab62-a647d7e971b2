import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/authStore'
import { ElMessage, ElMessageBox } from 'element-plus'

/**
 * 认证相关的组合式函数
 */
export function useAuth() {
  const router = useRouter()
  const authStore = useAuthStore()

  // 计算属性
  const isLoggedIn = computed(() => authStore.isLoggedIn)
  const user = computed(() => authStore.user)
  const isLoading = computed(() => authStore.isLoading)

  /**
   * 登录
   */
  const login = async (username, password) => {
    try {
      await authStore.login(username, password)
      return true
    } catch (error) {
      return false
    }
  }

  /**
   * 退出登录
   */
  const logout = async (showConfirm = true) => {
    if (showConfirm) {
      try {
        await ElMessageBox.confirm(
          '确定要退出登录吗？',
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        )
      } catch {
        return false
      }
    }

    await authStore.logout()
    router.push('/login')
    return true
  }

  /**
   * 检查认证状态
   */
  const checkAuth = () => {
    return authStore.checkAuthStatus()
  }

  /**
   * 验证token有效性
   */
  const validateAuth = async () => {
    return await authStore.validateAuth()
  }

  /**
   * 刷新用户信息
   */
  const refreshUser = async () => {
    try {
      await authStore.refreshUserInfo()
      return true
    } catch (error) {
      return false
    }
  }

  /**
   * 更新用户信息
   */
  const updateProfile = async (profileData) => {
    try {
      await authStore.updateProfile(profileData)
      return true
    } catch (error) {
      return false
    }
  }

  /**
   * 要求登录（如果未登录则跳转到登录页）
   */
  const requireAuth = (message = '请先登录') => {
    if (!isLoggedIn.value) {
      ElMessage.warning(message)
      router.push('/login')
      return false
    }
    return true
  }

  /**
   * 检查权限（可扩展用于角色权限检查）
   */
  const hasPermission = (permission) => {
    if (!isLoggedIn.value) return false
    
    // 这里可以根据用户角色或权限进行检查
    // 目前简单返回已登录状态
    return true
  }

  /**
   * 安全执行需要认证的操作
   */
  const withAuth = async (callback, options = {}) => {
    const { 
      requireLogin = true, 
      validateToken = false,
      loginMessage = '请先登录',
      expiredMessage = '登录已过期，请重新登录'
    } = options

    // 检查是否需要登录
    if (requireLogin && !isLoggedIn.value) {
      ElMessage.warning(loginMessage)
      router.push('/login')
      return false
    }

    // 检查token有效性
    if (validateToken && isLoggedIn.value) {
      const isValid = await validateAuth()
      if (!isValid) {
        ElMessage.warning(expiredMessage)
        router.push('/login')
        return false
      }
    }

    // 执行回调
    try {
      return await callback()
    } catch (error) {
      console.error('认证操作执行失败:', error)
      throw error
    }
  }

  /**
   * 监听认证状态变化
   */
  const onAuthChange = (callback) => {
    // 这里可以实现认证状态变化的监听
    // 目前简单返回当前状态
    return isLoggedIn.value
  }

  return {
    // 状态
    isLoggedIn,
    user,
    isLoading,
    
    // 方法
    login,
    logout,
    checkAuth,
    validateAuth,
    refreshUser,
    updateProfile,
    requireAuth,
    hasPermission,
    withAuth,
    onAuthChange
  }
}
