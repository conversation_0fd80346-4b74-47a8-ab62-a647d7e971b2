import apiClient from './apiClient'

/**
 * 小说相关API
 */
export const novelApi = {
  
  /**
   * 获取小说列表（分页）
   */
  getNovels(current = 1, size = 10) {
    return apiClient.get('/novels', {
      params: { current, size }
    })
  },
  
  /**
   * 获取所有小说列表
   */
  getNovelList() {
    return apiClient.get('/novels/list')
  },
  
  /**
   * 获取小说详情
   */
  getNovel(id) {
    return apiClient.get(`/novels/${id}`)
  },
  
  /**
   * 创建小说
   */
  createNovel(novelData) {
    return apiClient.post('/novels', novelData)
  },
  
  /**
   * 更新小说
   */
  updateNovel(id, novelData) {
    return apiClient.put(`/novels/${id}`, novelData)
  },
  
  /**
   * 删除小说
   */
  deleteNovel(id) {
    return apiClient.delete(`/novels/${id}`)
  },
  
  /**
   * 更新小说字数统计
   */
  updateWordCount(id) {
    return apiClient.post(`/novels/${id}/update-word-count`)
  },

  /**
   * 上传小说文件
   */
  uploadNovel(formData) {
    return apiClient.post('/novels/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }
}

/**
 * 章节相关API
 */
export const chapterApi = {
  
  /**
   * 获取小说的章节列表
   */
  getChapters(novelId) {
    return apiClient.get(`/novels/${novelId}/chapters`)
  },
  
  /**
   * 获取章节详情
   */
  getChapter(novelId, chapterId) {
    return apiClient.get(`/novels/${novelId}/chapters/${chapterId}`)
  },
  
  /**
   * 创建章节
   */
  createChapter(novelId, chapterData) {
    return apiClient.post(`/novels/${novelId}/chapters`, chapterData)
  },
  
  /**
   * 更新章节
   */
  updateChapter(novelId, chapterId, chapterData) {
    return apiClient.put(`/novels/${novelId}/chapters/${chapterId}`, chapterData)
  },
  
  /**
   * 删除章节
   */
  deleteChapter(novelId, chapterId) {
    return apiClient.delete(`/novels/${novelId}/chapters/${chapterId}`)
  },
  
  /**
   * 更新章节顺序
   */
  updateChapterOrder(novelId, orderData) {
    return apiClient.put(`/novels/${novelId}/chapters/order`, orderData)
  }
}

/**
 * 人物相关API
 */
export const characterApi = {
  
  /**
   * 获取小说的人物列表
   */
  getCharacters(novelId) {
    return apiClient.get(`/novels/${novelId}/characters`)
  },

  /**
   * 获取人物详情
   */
  getCharacter(novelId, characterId) {
    return apiClient.get(`/novels/${novelId}/characters/${characterId}`)
  },
  
  /**
   * 创建人物
   */
  createCharacter(novelId, characterData) {
    return apiClient.post(`/novels/${novelId}/characters`, characterData)
  },
  
  /**
   * 更新人物
   */
  updateCharacter(novelId, characterId, characterData) {
    return apiClient.put(`/novels/${novelId}/characters/${characterId}`, characterData)
  },
  
  /**
   * 删除人物
   */
  deleteCharacter(novelId, characterId) {
    return apiClient.delete(`/novels/${novelId}/characters/${characterId}`)
  }
}

/**
 * 世界观设定相关API
 */
export const worldSettingApi = {

  /**
   * 获取小说的世界观设定列表
   */
  getWorldSettings(novelId) {
    return apiClient.get(`/novels/${novelId}/world-settings`)
  },

  /**
   * 获取世界观设定详情
   */
  getWorldSetting(novelId, worldSettingId) {
    return apiClient.get(`/novels/${novelId}/world-settings/${worldSettingId}`)
  },

  /**
   * 创建世界观设定
   */
  createWorldSetting(novelId, worldSettingData) {
    return apiClient.post(`/novels/${novelId}/world-settings`, worldSettingData)
  },

  /**
   * 更新世界观设定
   */
  updateWorldSetting(novelId, worldSettingId, worldSettingData) {
    return apiClient.put(`/novels/${novelId}/world-settings/${worldSettingId}`, worldSettingData)
  },

  /**
   * 删除世界观设定
   */
  deleteWorldSetting(novelId, worldSettingId) {
    return apiClient.delete(`/novels/${novelId}/world-settings/${worldSettingId}`)
  }
}

/**
 * 语料库相关API
 */
export const corpusApi = {

  /**
   * 获取小说的语料库列表
   */
  getCorpusList(novelId) {
    return apiClient.get(`/novels/${novelId}/corpus`)
  },

  /**
   * 获取语料库详情
   */
  getCorpus(novelId, corpusId) {
    return apiClient.get(`/novels/${novelId}/corpus/${corpusId}`)
  },

  /**
   * 创建语料库
   */
  createCorpus(novelId, corpusData) {
    return apiClient.post(`/novels/${novelId}/corpus`, corpusData)
  },

  /**
   * 更新语料库
   */
  updateCorpus(novelId, corpusId, corpusData) {
    return apiClient.put(`/novels/${novelId}/corpus/${corpusId}`, corpusData)
  },

  /**
   * 删除语料库
   */
  deleteCorpus(novelId, corpusId) {
    return apiClient.delete(`/novels/${novelId}/corpus/${corpusId}`)
  }
}

/**
 * 事件相关API
 */
export const eventApi = {

  /**
   * 获取小说的事件列表
   */
  getEventsList(novelId, params = {}) {
    const queryParams = new URLSearchParams()
    if (params.importance) queryParams.append('importance', params.importance)
    if (params.chapter) queryParams.append('chapter', params.chapter)

    const queryString = queryParams.toString()
    const url = `/novels/${novelId}/events${queryString ? '?' + queryString : ''}`
    return apiClient.get(url)
  },

  /**
   * 获取事件详情
   */
  getEvent(novelId, eventId) {
    return apiClient.get(`/novels/${novelId}/events/${eventId}`)
  },

  /**
   * 创建事件
   */
  createEvent(novelId, eventData) {
    return apiClient.post(`/novels/${novelId}/events`, eventData)
  },

  /**
   * 更新事件
   */
  updateEvent(novelId, eventId, eventData) {
    return apiClient.put(`/novels/${novelId}/events/${eventId}`, eventData)
  },

  /**
   * 删除事件
   */
  deleteEvent(novelId, eventId) {
    return apiClient.delete(`/novels/${novelId}/events/${eventId}`)
  }
}

/**
 * 小说类型相关API
 */
export const genreApi = {

  /**
   * 获取用户的小说类型列表
   */
  getGenres() {
    return apiClient.get('/genres')
  },

  /**
   * 获取类型详情
   */
  getGenre(genreId) {
    return apiClient.get(`/genres/${genreId}`)
  },

  /**
   * 根据代码获取类型
   */
  getGenreByCode(code) {
    return apiClient.get(`/genres/code/${code}`)
  },

  /**
   * 创建类型
   */
  createGenre(genreData) {
    return apiClient.post('/genres', genreData)
  },

  /**
   * 更新类型
   */
  updateGenre(genreId, genreData) {
    return apiClient.put(`/genres/${genreId}`, genreData)
  },

  /**
   * 删除类型
   */
  deleteGenre(genreId) {
    return apiClient.delete(`/genres/${genreId}`)
  },

  /**
   * 初始化默认类型
   */
  initDefaultGenres() {
    return apiClient.post('/genres/init-defaults')
  }
}

/**
 * 提示词相关API
 */
export const promptApi = {
  
  /**
   * 获取提示词列表
   */
  getPrompts(category = null) {
    return apiClient.get('/prompts', {
      params: category ? { category } : {}
    })
  },

  /**
   * 获取提示词详情
   */
  getPrompt(id) {
    return apiClient.get(`/prompts/${id}`)
  },
  
  /**
   * 创建提示词
   */
  createPrompt(promptData) {
    return apiClient.post('/prompts', promptData)
  },
  
  /**
   * 更新提示词
   */
  updatePrompt(id, promptData) {
    return apiClient.put(`/prompts/${id}`, promptData)
  },
  
  /**
   * 删除提示词
   */
  deletePrompt(id) {
    return apiClient.delete(`/prompts/${id}`)
  },

  /**
   * 增加提示词使用次数
   */
  incrementUsageCount(id) {
    return apiClient.post(`/prompts/${id}/use`)
  }
}

/**
 * 写作目标相关API
 */
export const goalApi = {

  /**
   * 获取写作目标列表
   */
  getGoals() {
    return apiClient.get('/goals')
  },

  /**
   * 获取写作目标详情
   */
  getGoal(id) {
    return apiClient.get(`/goals/${id}`)
  },

  /**
   * 创建写作目标
   */
  createGoal(goalData) {
    return apiClient.post('/goals', goalData)
  },

  /**
   * 更新写作目标
   */
  updateGoal(id, goalData) {
    return apiClient.put(`/goals/${id}`, goalData)
  },

  /**
   * 更新目标进度
   */
  updateProgress(id, progressData) {
    return apiClient.post(`/goals/${id}/progress`, progressData)
  },

  /**
   * 删除写作目标
   */
  deleteGoal(id) {
    return apiClient.delete(`/goals/${id}`)
  },

  /**
   * 获取目标统计信息
   */
  getGoalStats() {
    return apiClient.get('/goals/stats')
  }
}
