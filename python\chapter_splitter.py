#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小说章节自动切分核心模块
实现智能章节识别和切分算法
"""

import re
import json
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass, asdict
from chapter_patterns import chapter_patterns, ChapterTitleCleaner


@dataclass
class Chapter:
    """章节数据结构"""
    chapter_number: int
    title: str
    start_position: int
    end_position: int
    content: str
    word_count: int
    confidence: float  # 识别置信度 0-1
    pattern_used: str  # 使用的模式名称


@dataclass
class NovelSplitResult:
    """小说切分结果"""
    title: str
    total_chapters: int
    total_words: int
    chapters: List[Chapter]
    split_method: str
    confidence: float
    warnings: List[str]


class ChapterSplitter:
    """章节切分器"""
    
    def __init__(self):
        self.patterns = chapter_patterns
        self.cleaner = ChapterTitleCleaner()
        
    def split_novel(self, text: str, novel_title: str = "未知小说") -> NovelSplitResult:
        """
        切分小说章节
        
        Args:
            text: 小说全文
            novel_title: 小说标题
            
        Returns:
            NovelSplitResult: 切分结果
        """
        if not text or not text.strip():
            raise ValueError("输入文本不能为空")
        
        # 预处理文本
        processed_text = self._preprocess_text(text)
        
        # 尝试多种切分策略
        strategies = [
            self._split_by_patterns,
            self._split_by_length_heuristic,
            self._split_by_paragraph_analysis
        ]
        
        best_result = None
        best_confidence = 0
        
        for strategy in strategies:
            try:
                result = strategy(processed_text, novel_title)
                if result.confidence > best_confidence:
                    best_result = result
                    best_confidence = result.confidence
            except Exception as e:
                print(f"策略 {strategy.__name__} 失败: {e}")
                continue
        
        if not best_result:
            # 如果所有策略都失败，创建单章节结果
            best_result = self._create_single_chapter_result(processed_text, novel_title)
        
        return best_result
    
    def _preprocess_text(self, text: str) -> str:
        """预处理文本"""
        # 统一换行符
        text = text.replace('\r\n', '\n').replace('\r', '\n')
        
        # 移除过多的空行
        text = re.sub(r'\n{3,}', '\n\n', text)
        
        # 移除行首行尾空白
        lines = [line.strip() for line in text.split('\n')]
        text = '\n'.join(lines)
        
        return text
    
    def _split_by_patterns(self, text: str, novel_title: str) -> NovelSplitResult:
        """基于模式的章节切分"""
        chapters = []
        warnings = []
        
        # 尝试每个模式
        for pattern_obj in self.patterns.get_patterns():
            matches = list(pattern_obj.pattern.finditer(text))
            if len(matches) >= 2:  # 至少需要2个匹配才认为是有效模式
                chapters = self._extract_chapters_from_matches(
                    text, matches, pattern_obj.name
                )
                if chapters:
                    return NovelSplitResult(
                        title=novel_title,
                        total_chapters=len(chapters),
                        total_words=len(text),
                        chapters=chapters,
                        split_method=f"pattern_{pattern_obj.name}",
                        confidence=self._calculate_pattern_confidence(chapters, text),
                        warnings=warnings
                    )
        
        raise ValueError("未找到合适的章节模式")
    
    def _extract_chapters_from_matches(self, text: str, matches: List, pattern_name: str) -> List[Chapter]:
        """从匹配结果中提取章节"""
        chapters = []
        
        for i, match in enumerate(matches):
            start_pos = match.start()
            end_pos = matches[i + 1].start() if i + 1 < len(matches) else len(text)
            
            # 提取章节标题
            title_match = match.group(0).strip()
            if len(match.groups()) > 0 and match.group(1):
                title = self.cleaner.clean_title(match.group(1))
            else:
                title = self.cleaner.clean_title(title_match)
            
            # 如果标题为空，使用默认标题
            if not title:
                title = f"第{i + 1}章"
            
            # 提取章节内容
            content = text[start_pos:end_pos].strip()
            
            # 计算章节号
            chapter_number = self.cleaner.extract_chapter_number(title_match)
            if chapter_number == 0:
                chapter_number = i + 1
            
            chapter = Chapter(
                chapter_number=chapter_number,
                title=title,
                start_position=start_pos,
                end_position=end_pos,
                content=content,
                word_count=len(content),
                confidence=0.8,  # 基于模式的匹配给予较高置信度
                pattern_used=pattern_name
            )
            
            chapters.append(chapter)
        
        return chapters
    
    def _split_by_length_heuristic(self, text: str, novel_title: str) -> NovelSplitResult:
        """基于长度启发式的章节切分"""
        warnings = []
        
        # 估算章节数量（基于平均章节长度）
        avg_chapter_length = 3000  # 假设平均章节长度
        estimated_chapters = max(1, len(text) // avg_chapter_length)
        
        if estimated_chapters == 1:
            return self._create_single_chapter_result(text, novel_title)
        
        chapters = []
        current_pos = 0
        
        for i in range(estimated_chapters):
            if current_pos >= len(text):
                break
                
            # 计算章节结束位置
            if i == estimated_chapters - 1:
                end_pos = len(text)
            else:
                target_pos = current_pos + avg_chapter_length
                end_pos = self._find_best_break_point(text, target_pos)
            
            content = text[current_pos:end_pos].strip()
            
            chapter = Chapter(
                chapter_number=i + 1,
                title=f"第{i + 1}章",
                start_position=current_pos,
                end_position=end_pos,
                content=content,
                word_count=len(content),
                confidence=0.5,  # 启发式方法置信度较低
                pattern_used="length_heuristic"
            )
            
            chapters.append(chapter)
            current_pos = end_pos
        
        warnings.append("使用长度启发式切分，可能不够准确")
        
        return NovelSplitResult(
            title=novel_title,
            total_chapters=len(chapters),
            total_words=len(text),
            chapters=chapters,
            split_method="length_heuristic",
            confidence=0.5,
            warnings=warnings
        )
    
    def _split_by_paragraph_analysis(self, text: str, novel_title: str) -> NovelSplitResult:
        """基于段落分析的章节切分"""
        warnings = []
        paragraphs = text.split('\n\n')
        
        # 寻找可能的章节分界点
        potential_breaks = []
        
        for i, paragraph in enumerate(paragraphs):
            if self._is_potential_chapter_start(paragraph):
                potential_breaks.append(i)
        
        if len(potential_breaks) < 2:
            warnings.append("段落分析未找到足够的章节分界点")
            return self._split_by_length_heuristic(text, novel_title)
        
        chapters = []
        current_pos = 0
        
        for i, break_idx in enumerate(potential_breaks):
            if i == len(potential_breaks) - 1:
                end_pos = len(text)
            else:
                # 计算下一个分界点的位置
                next_break_idx = potential_breaks[i + 1]
                end_pos = sum(len(p) + 2 for p in paragraphs[:next_break_idx])
            
            content = text[current_pos:end_pos].strip()
            title = self._extract_title_from_paragraph(paragraphs[break_idx])
            
            chapter = Chapter(
                chapter_number=i + 1,
                title=title or f"第{i + 1}章",
                start_position=current_pos,
                end_position=end_pos,
                content=content,
                word_count=len(content),
                confidence=0.6,
                pattern_used="paragraph_analysis"
            )
            
            chapters.append(chapter)
            current_pos = end_pos
        
        return NovelSplitResult(
            title=novel_title,
            total_chapters=len(chapters),
            total_words=len(text),
            chapters=chapters,
            split_method="paragraph_analysis",
            confidence=0.6,
            warnings=warnings
        )
    
    def _find_best_break_point(self, text: str, target_pos: int) -> int:
        """寻找最佳断点"""
        search_range = 200  # 搜索范围
        start = max(0, target_pos - search_range)
        end = min(len(text), target_pos + search_range)
        
        # 优先级：双换行 > 句号 > 感叹号 > 问号 > 换行
        break_chars = ['\n\n', '。', '！', '？', '\n']
        
        for char in break_chars:
            pos = text.rfind(char, start, end)
            if pos != -1:
                return pos + len(char)
        
        return target_pos
    
    def _is_potential_chapter_start(self, paragraph: str) -> bool:
        """判断段落是否可能是章节开始"""
        paragraph = paragraph.strip()
        
        # 长度检查
        if len(paragraph) > 100 or len(paragraph) < 2:
            return False
        
        # 包含章节关键词
        chapter_keywords = ['章', '节', '回', 'Chapter', 'chapter']
        if any(keyword in paragraph for keyword in chapter_keywords):
            return True
        
        # 数字开头
        if re.match(r'^\d+[\.、\s]', paragraph):
            return True
        
        return False
    
    def _extract_title_from_paragraph(self, paragraph: str) -> str:
        """从段落中提取标题"""
        paragraph = paragraph.strip()
        
        # 如果段落很短，直接作为标题
        if len(paragraph) <= 50:
            return self.cleaner.clean_title(paragraph)
        
        # 提取第一行作为标题
        first_line = paragraph.split('\n')[0].strip()
        return self.cleaner.clean_title(first_line)
    
    def _calculate_pattern_confidence(self, chapters: List[Chapter], text: str) -> float:
        """计算模式匹配的置信度"""
        if not chapters:
            return 0.0
        
        # 基础置信度
        confidence = 0.7
        
        # 章节数量合理性
        if 2 <= len(chapters) <= 200:
            confidence += 0.1
        
        # 章节长度分布合理性
        lengths = [ch.word_count for ch in chapters]
        avg_length = sum(lengths) / len(lengths)
        
        # 检查长度变异系数
        if avg_length > 0:
            variance = sum((l - avg_length) ** 2 for l in lengths) / len(lengths)
            cv = (variance ** 0.5) / avg_length
            if cv < 1.0:  # 变异系数小于1认为比较合理
                confidence += 0.1
        
        # 章节标题质量
        title_quality = sum(1 for ch in chapters if len(ch.title) > 0) / len(chapters)
        confidence += title_quality * 0.1
        
        return min(1.0, confidence)
    
    def _create_single_chapter_result(self, text: str, novel_title: str) -> NovelSplitResult:
        """创建单章节结果"""
        chapter = Chapter(
            chapter_number=1,
            title="全文",
            start_position=0,
            end_position=len(text),
            content=text,
            word_count=len(text),
            confidence=0.3,
            pattern_used="single_chapter"
        )
        
        return NovelSplitResult(
            title=novel_title,
            total_chapters=1,
            total_words=len(text),
            chapters=[chapter],
            split_method="single_chapter",
            confidence=0.3,
            warnings=["未能识别章节结构，作为单章节处理"]
        )
    
    def export_to_json(self, result: NovelSplitResult, include_content: bool = True) -> str:
        """导出结果为JSON格式"""
        # 转换为字典
        result_dict = asdict(result)
        
        # 如果不包含内容，移除content字段
        if not include_content:
            for chapter in result_dict['chapters']:
                chapter.pop('content', None)
        
        return json.dumps(result_dict, ensure_ascii=False, indent=2)
