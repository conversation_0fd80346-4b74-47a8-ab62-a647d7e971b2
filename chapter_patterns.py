#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
章节标识模式定义模块
定义各种常见的章节标题识别模式
"""

import re
from typing import List, Dict, Pattern
from dataclasses import dataclass


@dataclass
class ChapterPattern:
    """章节模式定义"""
    name: str
    pattern: Pattern[str]
    priority: int  # 优先级，数字越大优先级越高
    description: str


class ChapterPatterns:
    """章节模式管理类"""
    
    def __init__(self):
        self.patterns = self._init_patterns()
    
    def _init_patterns(self) -> List[ChapterPattern]:
        """初始化章节模式"""
        patterns = []
        
        # 中文数字映射
        chinese_numbers = {
            '一': 1, '二': 2, '三': 3, '四': 4, '五': 5, '六': 6, '七': 7, '八': 8, '九': 9, '十': 10,
            '十一': 11, '十二': 12, '十三': 13, '十四': 14, '十五': 15, '十六': 16, '十七': 17, '十八': 18, '十九': 19, '二十': 20,
            '二十一': 21, '二十二': 22, '二十三': 23, '二十四': 24, '二十五': 25, '二十六': 26, '二十七': 27, '二十八': 28, '二十九': 29, '三十': 30,
            '三十一': 31, '三十二': 32, '三十三': 33, '三十四': 34, '三十五': 35, '三十六': 36, '三十七': 37, '三十八': 38, '三十九': 39, '四十': 40,
            '四十一': 41, '四十二': 42, '四十三': 43, '四十四': 44, '四十五': 45, '四十六': 46, '四十七': 47, '四十八': 48, '四十九': 49, '五十': 50,
            '百': 100, '千': 1000, '万': 10000
        }
        
        # 1. 标准中文章节格式 - 最高优先级
        patterns.append(ChapterPattern(
            name="standard_chinese",
            pattern=re.compile(r'^第[一二三四五六七八九十百千万\d]+[章节]\s*(.*)$', re.MULTILINE | re.IGNORECASE),
            priority=100,
            description="标准中文章节格式：第X章/第X节"
        ))
        
        # 2. 英文章节格式
        patterns.append(ChapterPattern(
            name="english_chapter",
            pattern=re.compile(r'^Chapter\s+(\d+|[IVXLCDM]+)\s*[:\-]?\s*(.*)$', re.MULTILINE | re.IGNORECASE),
            priority=90,
            description="英文章节格式：Chapter X"
        ))
        
        # 3. 数字章节格式
        patterns.append(ChapterPattern(
            name="numeric_chapter",
            pattern=re.compile(r'^(\d+)[\.、\s]+(.*)$', re.MULTILINE),
            priority=80,
            description="数字章节格式：1. 或 1、"
        ))
        
        # 4. 中文数字章节格式
        patterns.append(ChapterPattern(
            name="chinese_number",
            pattern=re.compile(r'^[一二三四五六七八九十百千万]+[\.、\s]+(.*)$', re.MULTILINE),
            priority=85,
            description="中文数字章节格式：一、二、三、"
        ))
        
        # 5. 卷章格式
        patterns.append(ChapterPattern(
            name="volume_chapter",
            pattern=re.compile(r'^第[一二三四五六七八九十百千万\d]+卷\s+第[一二三四五六七八九十百千万\d]+[章节]\s*(.*)$', re.MULTILINE | re.IGNORECASE),
            priority=95,
            description="卷章格式：第X卷 第X章"
        ))
        
        # 6. 简化章节格式
        patterns.append(ChapterPattern(
            name="simple_chapter",
            pattern=re.compile(r'^[章节]\s*[一二三四五六七八九十百千万\d]+\s*[:\-]?\s*(.*)$', re.MULTILINE | re.IGNORECASE),
            priority=75,
            description="简化章节格式：章X 或 节X"
        ))
        
        # 7. 括号章节格式
        patterns.append(ChapterPattern(
            name="bracket_chapter",
            pattern=re.compile(r'^\(第[一二三四五六七八九十百千万\d]+[章节]\)\s*(.*)$', re.MULTILINE | re.IGNORECASE),
            priority=70,
            description="括号章节格式：(第X章)"
        ))
        
        # 8. 序号章节格式
        patterns.append(ChapterPattern(
            name="ordinal_chapter",
            pattern=re.compile(r'^第(\d+)回\s*(.*)$', re.MULTILINE | re.IGNORECASE),
            priority=85,
            description="序号章节格式：第X回"
        ))
        
        # 9. 自由格式章节（较宽松的匹配）
        patterns.append(ChapterPattern(
            name="free_format",
            pattern=re.compile(r'^.{0,50}[章节回]\s*[一二三四五六七八九十百千万\d]*\s*[:\-]?\s*(.{0,100})$', re.MULTILINE | re.IGNORECASE),
            priority=50,
            description="自由格式章节（宽松匹配）"
        ))
        
        return sorted(patterns, key=lambda x: x.priority, reverse=True)
    
    def get_patterns(self) -> List[ChapterPattern]:
        """获取所有模式，按优先级排序"""
        return self.patterns
    
    def get_pattern_by_name(self, name: str) -> ChapterPattern:
        """根据名称获取模式"""
        for pattern in self.patterns:
            if pattern.name == name:
                return pattern
        raise ValueError(f"Pattern '{name}' not found")
    
    def add_custom_pattern(self, name: str, pattern_str: str, priority: int, description: str):
        """添加自定义模式"""
        try:
            pattern = re.compile(pattern_str, re.MULTILINE | re.IGNORECASE)
            custom_pattern = ChapterPattern(name, pattern, priority, description)
            self.patterns.append(custom_pattern)
            self.patterns.sort(key=lambda x: x.priority, reverse=True)
        except re.error as e:
            raise ValueError(f"Invalid regex pattern: {e}")


class ChapterTitleCleaner:
    """章节标题清理工具"""
    
    @staticmethod
    def clean_title(title: str) -> str:
        """清理章节标题"""
        if not title:
            return ""
        
        # 移除前后空白
        title = title.strip()
        
        # 移除常见的无用字符
        title = re.sub(r'^[:\-\s]+', '', title)
        title = re.sub(r'[:\-\s]+$', '', title)
        
        # 移除多余的空白
        title = re.sub(r'\s+', ' ', title)
        
        return title
    
    @staticmethod
    def extract_chapter_number(text: str) -> int:
        """从文本中提取章节号"""
        # 中文数字映射
        chinese_to_num = {
            '一': 1, '二': 2, '三': 3, '四': 4, '五': 5, '六': 6, '七': 7, '八': 8, '九': 9, '十': 10,
            '十一': 11, '十二': 12, '十三': 13, '十四': 14, '十五': 15, '十六': 16, '十七': 17, '十八': 18, '十九': 19, '二十': 20
        }
        
        # 尝试提取阿拉伯数字
        num_match = re.search(r'\d+', text)
        if num_match:
            return int(num_match.group())
        
        # 尝试提取中文数字
        for chinese, num in chinese_to_num.items():
            if chinese in text:
                return num
        
        return 0


# 预定义的章节模式实例
chapter_patterns = ChapterPatterns()
