package com.writing.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 章节实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("chapters")
public class Chapter {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    @TableField("novel_id")
    private Long novelId;
    
    @TableField("title")
    private String title;
    
    @TableField("content")
    private String content;
    
    @TableField("summary")
    private String summary;
    
    @TableField("outline")
    private String outline;
    
    @TableField("notes")
    private String notes;
    
    @TableField("status")
    private String status;
    
    @TableField("word_count")
    private Integer wordCount;
    
    @TableField("chapter_order")
    private Integer chapterOrder;
    
    @TableField("ai_generated")
    private Integer aiGenerated;
    
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
    
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
    
    @TableLogic
    @TableField("deleted")
    private Integer deleted;
}
