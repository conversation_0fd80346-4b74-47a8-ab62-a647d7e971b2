package com.writing.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.writing.entity.User;

/**
 * 用户服务接口
 */
public interface UserService extends IService<User> {
    
    /**
     * 根据用户名查找用户
     */
    User findByUsername(String username);
    
    /**
     * 根据邮箱查找用户
     */
    User findByEmail(String email);
    
    /**
     * 用户注册
     */
    User register(String username, String email, String password);
    
    /**
     * 用户登录
     */
    String login(String username, String password);
    
    /**
     * 更新最后登录时间
     */
    void updateLastLoginTime(Long userId);
}
