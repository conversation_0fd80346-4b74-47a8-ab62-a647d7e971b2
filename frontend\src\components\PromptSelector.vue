<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    :title="dialogTitle"
    width="800px"
    @close="handleClose"
    class="prompt-selector-dialog"
  >
    <div class="prompt-dialog-content">
      <!-- 提示词列表 -->
      <div class="prompt-list">
        <h4>{{ getCategoryDisplayName() }} 提示词</h4>
        <div class="prompt-cards">
          <div
            v-for="prompt in filteredPrompts"
            :key="prompt.id"
            class="prompt-card"
            :class="{ active: selectedPrompt?.id === prompt.id }"
            @click="selectPrompt(prompt)"
          >
            <div class="prompt-card-header">
              <h5>{{ prompt.title }}</h5>
            </div>
            <div class="prompt-card-description">
              <p>{{ prompt.description }}</p>
            </div>
            <div class="prompt-card-tags">
              <el-tag v-for="tag in prompt.tags" :key="tag" size="small">{{ tag }}</el-tag>
            </div>
          </div>
        </div>

        <div v-if="filteredPrompts.length === 0" class="empty-prompts">
          <p>暂无该类型的提示词</p>
          <el-button type="primary" @click="$emit('goToPromptLibrary')">去提示词库添加</el-button>
        </div>
      </div>

      <!-- 变量填充区域 -->
      <div v-if="selectedPrompt && Object.keys(variables).length > 0" class="prompt-variables">
        <h4>填充变量</h4>
        <el-form label-width="120px" size="small">
          <el-form-item
            v-for="(value, variable) in variables"
            :key="variable"
            :label="variable + '：'"
          >
            <!-- 前文概要使用章节多选框 -->
            <div v-if="variable === '前文概要'" class="context-variable-container">
              <el-select
                v-model="contextChapters"
                multiple
                placeholder="选择章节作为前文参考"
                @change="updateContextVariable"
                size="small"
                style="width: 100%"
                max-collapse-tags="3"
              >
                <el-option
                  v-for="chapter in availableContextChapters"
                  :key="chapter.id"
                  :label="`第${chapter.chapterIndex}章 ${chapter.title} (${chapter.wordCount}字)`"
                  :value="chapter.id"
                >
                  <div class="context-chapter-option">
                    <span class="chapter-title">第{{ chapter.chapterIndex }}章 {{ chapter.title }}</span>
                    <div class="chapter-meta">
                      <el-tag :type="getChapterStatusType(chapter.status)" size="small">{{ getChapterStatusText(chapter.status) }}</el-tag>
                      <span class="word-count">{{ chapter.wordCount }}字</span>
                    </div>
                  </div>
                </el-option>
              </el-select>
              <div class="context-actions">
                <el-button size="small" @click="clearContextSelection" v-if="contextChapters.length > 0">清空</el-button>
              </div>
            </div>

            <!-- 其他变量使用普通输入框 -->
            <el-input
              v-else
              v-model="variables[variable]"
              :type="['章节大纲', '主要人物', '世界观设定', '参考语料'].includes(variable) ? 'textarea' : 'text'"
              :rows="2"
              :placeholder="'请输入' + variable"
              @input="generateFinalPrompt"
              size="small"
            />
          </el-form-item>
        </el-form>
      </div>

      <!-- 最终提示词预览 -->
      <div v-if="selectedPrompt" class="final-prompt">
        <h4>最终提示词预览</h4>
        <el-input
          v-model="finalPrompt"
          type="textarea"
          :rows="8"
          readonly
          placeholder="请先选择提示词并填充变量"
        />
      </div>
    </div>

    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button v-if="selectedPrompt" @click="copyPromptToClipboard">复制提示词</el-button>
      <el-button 
        v-if="selectedPrompt" 
        type="primary" 
        @click="handleConfirm"
        :loading="loading"
      >
        {{ loading ? '处理中...' : '使用此提示词' }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  category: {
    type: String,
    default: ''
  },
  prompts: {
    type: Array,
    default: () => []
  },
  autoFillData: {
    type: Object,
    default: () => ({})
  },
  availableContextChapters: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '选择提示词'
  }
})

// Emits
const emit = defineEmits([
  'update:visible',
  'confirm',
  'cancel',
  'goToPromptLibrary'
])

// 响应式数据
const selectedPrompt = ref(null)
const variables = ref({})
const finalPrompt = ref('')
const contextChapters = ref([])

// 计算属性
const dialogTitle = computed(() => props.title)

const filteredPrompts = computed(() => {
  return props.prompts.filter(prompt => prompt.category === props.category)
})

// 分类显示名称映射
const categoryNames = {
  outline: '章节大纲',
  content: '基础正文',
  'content-dialogue': '对话生成',
  'content-scene': '场景描写',
  'content-action': '动作描写',
  character: '人物生成',
  worldview: '世界观生成',
  optimize: '文本优化',
  continue: '续写生成'
}

const getCategoryDisplayName = () => {
  return categoryNames[props.category] || '提示词'
}

// 方法
const selectPrompt = (prompt) => {
  selectedPrompt.value = prompt
  variables.value = {}

  // 提取变量
  const matches = prompt.content.match(/\{([^}]+)\}/g)
  if (matches) {
    matches.forEach(match => {
      const variable = match.slice(1, -1)
      variables.value[variable] = ''
    })
  }

  // 自动填充变量
  autoFillVariables()
  generateFinalPrompt()
}

const autoFillVariables = () => {
  if (!selectedPrompt.value || !props.autoFillData) return

  // 根据传入的自动填充数据填充变量
  Object.keys(variables.value).forEach(variable => {
    if (props.autoFillData[variable]) {
      variables.value[variable] = props.autoFillData[variable]
    }
  })
}

const generateFinalPrompt = () => {
  if (!selectedPrompt.value) {
    finalPrompt.value = ''
    return
  }

  let result = selectedPrompt.value.content
  Object.keys(variables.value).forEach(variable => {
    const value = variables.value[variable] || `{${variable}}`
    result = result.replace(new RegExp(`\\{${variable}\\}`, 'g'), value)
  })

  finalPrompt.value = result
}

const updateContextVariable = () => {
  if (!contextChapters.value.length) {
    variables.value['前文概要'] = ''
    generateFinalPrompt()
    return
  }

  // 根据选中的章节生成前文概要
  const selectedChapters = props.availableContextChapters.filter(
    chapter => contextChapters.value.includes(chapter.id)
  )

  const chaptersInfo = selectedChapters.map(chapter => {
    const cleanContent = chapter.content ? 
      chapter.content.replace(/<[^>]*>/g, '').replace(/&nbsp;/g, ' ').trim() : ''
    
    return `第${chapter.chapterIndex}章 ${chapter.title}：
大纲：${chapter.description || '暂无大纲'}
${cleanContent ? `内容概要：${cleanContent.substring(0, 200)}...` : ''}`
  }).join('\n\n')

  variables.value['前文概要'] = chaptersInfo
  generateFinalPrompt()
}

const clearContextSelection = () => {
  contextChapters.value = []
  variables.value['前文概要'] = ''
  generateFinalPrompt()
}

const copyPromptToClipboard = async () => {
  try {
    await navigator.clipboard.writeText(finalPrompt.value)
    ElMessage.success('提示词已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败，请手动复制')
  }
}

const handleConfirm = () => {
  if (!selectedPrompt.value || !finalPrompt.value) {
    ElMessage.warning('请选择提示词并填充变量')
    return
  }

  emit('confirm', {
    prompt: selectedPrompt.value,
    variables: variables.value,
    finalPrompt: finalPrompt.value,
    contextChapters: contextChapters.value
  })
}

const handleCancel = () => {
  emit('cancel')
  handleClose()
}

const handleClose = () => {
  // 重置状态
  selectedPrompt.value = null
  variables.value = {}
  finalPrompt.value = ''
  contextChapters.value = []
  emit('update:visible', false)
}

// 章节状态相关方法（从父组件传入或在这里定义默认实现）
const getChapterStatusType = (status) => {
  const statusTypes = {
    draft: 'info',
    completed: 'success',
    published: 'warning'
  }
  return statusTypes[status] || 'info'
}

const getChapterStatusText = (status) => {
  const statusTexts = {
    draft: '草稿',
    completed: '完成',
    published: '发表'
  }
  return statusTexts[status] || '草稿'
}

// 监听变量变化
watch(variables, () => {
  generateFinalPrompt()
}, { deep: true })

// 监听对话框显示状态
watch(() => props.visible, (newVal) => {
  if (!newVal) {
    handleClose()
  }
})
</script>

<style scoped>
.prompt-selector-dialog {
  .prompt-dialog-content {
    max-height: 70vh;
    overflow-y: auto;
  }

  .prompt-list h4 {
    margin-bottom: 16px;
    color: #303133;
    font-weight: 600;
  }

  .prompt-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 12px;
    margin-bottom: 20px;
  }

  .prompt-card {
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    padding: 16px;
    cursor: pointer;
    transition: all 0.3s;
    background: #fff;

    &:hover {
      border-color: #409eff;
      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
    }

    &.active {
      border-color: #409eff;
      background: #f0f9ff;
      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
    }
  }

  .prompt-card-header h5 {
    margin: 0 0 8px 0;
    color: #303133;
    font-size: 14px;
    font-weight: 600;
  }

  .prompt-card-description p {
    margin: 0 0 12px 0;
    color: #606266;
    font-size: 12px;
    line-height: 1.4;
  }

  .prompt-card-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
  }

  .empty-prompts {
    text-align: center;
    padding: 40px;
    color: #909399;
  }

  .prompt-variables {
    margin-top: 24px;
    padding-top: 24px;
    border-top: 1px solid #e4e7ed;

    h4 {
      margin-bottom: 16px;
      color: #303133;
      font-weight: 600;
    }
  }

  .context-variable-container {
    .context-actions {
      margin-top: 8px;
    }
  }

  .context-chapter-option {
    .chapter-title {
      display: block;
      font-weight: 500;
    }

    .chapter-meta {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-top: 4px;
      font-size: 12px;
      color: #909399;

      .word-count {
        color: #606266;
      }
    }
  }

  .final-prompt {
    margin-top: 24px;
    padding-top: 24px;
    border-top: 1px solid #e4e7ed;

    h4 {
      margin-bottom: 16px;
      color: #303133;
      font-weight: 600;
    }
  }
}
</style>
