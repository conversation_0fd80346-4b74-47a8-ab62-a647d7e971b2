import apiClient from './apiClient'

/**
 * 文件上传相关API
 */
export const uploadApi = {
  
  /**
   * 上传图片
   */
  uploadImage(file) {
    const formData = new FormData()
    formData.append('file', file)
    
    return apiClient.post('/upload/image', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },
  
  /**
   * 上传小说封面
   */
  uploadCover(file) {
    const formData = new FormData()
    formData.append('file', file)

    return apiClient.post('/upload/cover', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * 上传用户头像
   */
  uploadAvatar(file) {
    const formData = new FormData()
    formData.append('file', file)

    return apiClient.post('/upload/avatar', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },
  
  /**
   * 上传文档
   */
  uploadDocument(file) {
    const formData = new FormData()
    formData.append('file', file)
    
    return apiClient.post('/upload/document', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },
  
  /**
   * 删除文件
   */
  deleteFile(fileUrl) {
    return apiClient.delete('/upload/file', {
      params: { url: fileUrl }
    })
  }
}

export default uploadApi
