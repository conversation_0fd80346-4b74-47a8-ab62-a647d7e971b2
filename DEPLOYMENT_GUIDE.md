# 写作应用部署指南

## 快速开始

### 1. 环境要求

**后端环境：**
- JDK 17 或更高版本
- Maven 3.6+
- MySQL 8.0+
- Redis (可选，用于缓存)

**前端环境：**
- Node.js 16+
- npm 或 yarn

### 2. 数据库设置

1. 创建数据库：
```sql
CREATE DATABASE writing_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 创建用户并授权：
```sql
CREATE USER 'writing_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON writing_db.* TO 'writing_user'@'localhost';
FLUSH PRIVILEGES;
```

3. 执行初始化脚本：
```bash
mysql -u writing_user -p writing_db < backend/src/main/resources/db/migration/V1__init_database.sql
```

### 3. 后端部署

1. 修改配置文件 `backend/src/main/resources/application.yml`：
```yaml
spring:
  datasource:
    url: ***********************************************************************************************************************
    username: writing_user
    password: your_password
    
jwt:
  secret: your-secret-key-here  # 请修改为你的密钥
  expiration: 86400000  # 24小时
```

2. 编译和运行：
```bash
cd backend
mvn clean package -DskipTests
java -jar target/writing-backend-1.0.0.jar
```

或者使用Maven直接运行：
```bash
cd backend
mvn spring-boot:run
```

后端服务将在 `http://localhost:8080` 启动。

### 4. 前端部署

1. 安装依赖：
```bash
cd frontend
npm install
```

2. 修改API配置（如果需要）：
编辑 `frontend/src/services/apiClient.js`：
```javascript
const apiClient = axios.create({
  baseURL: 'http://localhost:8080/api', // 修改为你的后端地址
  // ...
})
```

3. 开发模式运行：
```bash
npm run dev
```

4. 生产构建：
```bash
npm run build
```

构建后的文件在 `dist` 目录中，可以部署到任何静态文件服务器。

## 生产环境部署

### 1. 使用Docker部署

创建 `docker-compose.yml`：
```yaml
version: '3.8'
services:
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: writing_db
      MYSQL_USER: writing_user
      MYSQL_PASSWORD: user_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./backend/src/main/resources/db/migration:/docker-entrypoint-initdb.d

  backend:
    build: ./backend
    ports:
      - "8080:8080"
    depends_on:
      - mysql
    environment:
      SPRING_DATASOURCE_URL: **********************************
      SPRING_DATASOURCE_USERNAME: writing_user
      SPRING_DATASOURCE_PASSWORD: user_password

  frontend:
    build: ./frontend
    ports:
      - "80:80"
    depends_on:
      - backend

volumes:
  mysql_data:
```

### 2. 使用Nginx部署前端

Nginx配置示例：
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    root /var/www/writing-app;
    index index.html;
    
    # 前端路由
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # API代理
    location /api/ {
        proxy_pass http://localhost:8080/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 测试指南

### 1. 后端API测试

使用curl测试API：

1. 注册用户：
```bash
curl -X POST http://localhost:8080/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "123456"
  }'
```

2. 用户登录：
```bash
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "123456"
  }'
```

3. 获取小说列表（需要token）：
```bash
curl -X GET http://localhost:8080/api/novels \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### 2. 前端功能测试

1. 访问 `http://localhost:5173`（开发模式）
2. 测试用户注册和登录
3. 测试小说创建、编辑、删除
4. 测试章节管理
5. 测试提示词库
6. 测试写作目标

### 3. 数据迁移测试

如果有现有的localStorage数据，可以编写迁移脚本：

```javascript
// 迁移脚本示例
async function migrateLocalStorageData() {
  const novels = JSON.parse(localStorage.getItem('novels') || '[]')
  
  for (const novel of novels) {
    try {
      // 创建小说
      const createdNovel = await novelApi.createNovel({
        title: novel.title,
        description: novel.description,
        genre: novel.genre,
        // ... 其他字段
      })
      
      // 迁移章节
      if (novel.chapterList) {
        for (const chapter of novel.chapterList) {
          await chapterApi.createChapter(createdNovel.id, {
            title: chapter.title,
            content: chapter.content,
            // ... 其他字段
          })
        }
      }
      
      console.log(`小说 "${novel.title}" 迁移成功`)
    } catch (error) {
      console.error(`小说 "${novel.title}" 迁移失败:`, error)
    }
  }
}
```

## 性能优化

### 1. 后端优化

1. **数据库优化**：
   - 添加适当的索引
   - 使用连接池
   - 配置查询缓存

2. **API优化**：
   - 实现分页查询
   - 添加Redis缓存
   - 使用异步处理

3. **安全优化**：
   - 配置HTTPS
   - 实现请求限流
   - 添加SQL注入防护

### 2. 前端优化

1. **代码分割**：
   - 路由懒加载
   - 组件懒加载

2. **缓存策略**：
   - 实现本地缓存
   - 使用HTTP缓存

3. **用户体验**：
   - 添加加载状态
   - 实现离线提示
   - 优化错误处理

## 监控和日志

### 1. 后端监控

添加Spring Boot Actuator：
```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-actuator</artifactId>
</dependency>
```

### 2. 日志配置

配置logback-spring.xml：
```xml
<configuration>
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>
    
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/writing-app.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/writing-app.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>
    
    <root level="INFO">
        <appender-ref ref="STDOUT" />
        <appender-ref ref="FILE" />
    </root>
</configuration>
```

## 故障排除

### 常见问题

1. **数据库连接失败**：
   - 检查数据库服务是否启动
   - 验证连接字符串和凭据
   - 确认防火墙设置

2. **CORS错误**：
   - 检查后端CORS配置
   - 确认前端API地址正确

3. **JWT认证失败**：
   - 检查token是否正确传递
   - 验证JWT密钥配置
   - 确认token未过期

4. **前端路由问题**：
   - 检查路由守卫配置
   - 确认登录状态检查逻辑

### 调试技巧

1. 启用详细日志
2. 使用浏览器开发者工具
3. 检查网络请求和响应
4. 使用Postman测试API

## 备份和恢复

### 数据库备份

```bash
# 备份
mysqldump -u writing_user -p writing_db > backup.sql

# 恢复
mysql -u writing_user -p writing_db < backup.sql
```

### 应用数据备份

实现数据导出API，定期备份用户数据。
