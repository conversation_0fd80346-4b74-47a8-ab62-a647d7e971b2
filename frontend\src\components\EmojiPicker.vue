<template>
  <div class="emoji-picker">
    <el-popover
      ref="popoverRef"
      placement="bottom-start"
      :width="320"
      trigger="click"
      popper-class="emoji-popover"
    >
      <template #reference>
        <div class="emoji-trigger" @click="handleTriggerClick">
          <span class="current-emoji">{{ modelValue || '📖' }}</span>
          <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
        </div>
      </template>
      
      <div class="emoji-picker-content">
        <div class="emoji-categories">
          <el-tabs v-model="activeCategory" type="card" size="small">
            <el-tab-pane 
              v-for="(category, key) in emojiCategories" 
              :key="key"
              :label="category.name" 
              :name="key"
            >
              <div class="emoji-grid">
                <div 
                  v-for="emoji in category.emojis" 
                  :key="emoji"
                  class="emoji-item"
                  :class="{ active: modelValue === emoji }"
                  @click="selectEmoji(emoji)"
                  :title="getEmojiTitle(emoji)"
                >
                  {{ emoji }}
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
        
        <div class="emoji-actions">
          <el-button size="small" @click="selectEmoji('')">
            清除
          </el-button>
          <el-button size="small" type="primary" @click="closePopover">
            确定
          </el-button>
        </div>
      </div>
    </el-popover>
  </div>
</template>

<script setup>
import { ref, defineEmits, defineProps } from 'vue'
import { ArrowDown } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  modelValue: {
    type: String,
    default: '📖'
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 响应式数据
const activeCategory = ref('books')
const popoverRef = ref()

// Emoji分类数据
const emojiCategories = {
  books: {
    name: '📚 书籍',
    emojis: ['📖', '📚', '📝', '📜', '📋', '📄', '📃', '📑', '🗞️', '📰', '📓', '📔', '📕', '📗', '📘', '📙']
  },
  fantasy: {
    name: '⚡ 玄幻',
    emojis: ['⚡', '🔮', '🧙‍♂️', '🧙‍♀️', '🗡️', '⚔️', '🛡️', '🏰', '🐉', '🦄', '🧚‍♂️', '🧚‍♀️', '✨', '💫', '🌟', '⭐']
  },
  urban: {
    name: '🏙️ 都市',
    emojis: ['🏙️', '🌆', '🏢', '🏬', '🏪', '🏫', '🏦', '🏨', '🚗', '🚕', '🚌', '🚇', '💼', '👔', '💻', '📱']
  },
  history: {
    name: '📜 历史',
    emojis: ['📜', '🏛️', '👑', '⚔️', '🛡️', '🏺', '🗿', '🕌', '🏯', '🏰', '🎭', '🎨', '🖼️', '📿', '⚱️', '🏺']
  },
  scifi: {
    name: '🚀 科幻',
    emojis: ['🚀', '🛸', '👽', '🤖', '🔬', '🧪', '⚗️', '🔭', '🌌', '🌠', '🪐', '🌍', '🌎', '🌏', '💫', '⭐']
  },
  wuxia: {
    name: '⚔️ 武侠',
    emojis: ['⚔️', '🗡️', '🥋', '🥷', '👺', '🎋', '🏔️', '🌸', '🍃', '🌿', '🎍', '🏮', '🎌', '⛩️', '🏯', '🗾']
  },
  romance: {
    name: '💕 言情',
    emojis: ['💕', '💖', '💗', '💘', '💝', '💞', '💟', '❤️', '🧡', '💛', '💚', '💙', '💜', '🤍', '🖤', '🤎']
  },
  symbols: {
    name: '🔥 符号',
    emojis: ['🔥', '💎', '👑', '🎯', '🎪', '🎭', '🎨', '🎵', '🎶', '🎼', '🎹', '🎸', '🎺', '🎷', '🥁', '🎤']
  },
  nature: {
    name: '🌟 自然',
    emojis: ['🌟', '⭐', '✨', '💫', '🌙', '☀️', '🌈', '⚡', '🔥', '💧', '🌊', '🌀', '❄️', '☃️', '🌸', '🌺']
  },
  animals: {
    name: '🐉 动物',
    emojis: ['🐉', '🦄', '🦅', '🦆', '🐺', '🦊', '🐱', '🐶', '🐯', '🦁', '🐸', '🐢', '🦋', '🐝', '🦀', '🐙']
  }
}

// Emoji标题映射
const emojiTitles = {
  '📖': '打开的书',
  '📚': '书籍',
  '📝': '备忘录',
  '📜': '卷轴',
  '⚡': '闪电',
  '🔮': '水晶球',
  '🧙‍♂️': '男法师',
  '🧙‍♀️': '女法师',
  '🗡️': '匕首',
  '⚔️': '交叉剑',
  '🛡️': '盾牌',
  '🏰': '城堡',
  '🐉': '龙',
  '🦄': '独角兽',
  '🏙️': '城市景观',
  '🌆': '黄昏城市',
  '🏢': '办公楼',
  '💼': '公文包',
  '👔': '领带',
  '💻': '笔记本电脑',
  '📱': '手机',
  '🏛️': '古典建筑',
  '👑': '皇冠',
  '🏺': '双耳瓶',
  '🗿': '摩艾石像',
  '🚀': '火箭',
  '🛸': '飞碟',
  '👽': '外星人',
  '🤖': '机器人',
  '🔬': '显微镜',
  '🧪': '试管',
  '🔭': '望远镜',
  '🌌': '银河',
  '🌠': '流星',
  '🪐': '土星',
  '🥋': '武术服',
  '🥷': '忍者',
  '👺': '天狗',
  '🎋': '七夕树',
  '🏔️': '雪山',
  '🌸': '樱花',
  '🍃': '叶子',
  '🏮': '红灯笼',
  '⛩️': '神社',
  '🏯': '日式城堡',
  '💕': '两颗心',
  '💖': '闪亮的心',
  '💗': '跳动的心',
  '💘': '丘比特之箭',
  '❤️': '红心',
  '🔥': '火焰',
  '💎': '钻石',
  '🎯': '靶心',
  '🎪': '马戏团',
  '🎭': '戏剧面具',
  '🎨': '调色板',
  '🌟': '闪亮星星',
  '⭐': '星星',
  '✨': '闪光',
  '💫': '眩晕',
  '🌙': '新月',
  '☀️': '太阳',
  '🌈': '彩虹',
  '🌊': '海浪',
  '❄️': '雪花',
  '🦅': '老鹰',
  '🦆': '鸭子',
  '🐺': '狼',
  '🦊': '狐狸',
  '🐱': '猫脸',
  '🐶': '狗脸',
  '🐯': '老虎脸',
  '🦁': '狮子脸',
  '🐸': '青蛙',
  '🐢': '乌龟',
  '🦋': '蝴蝶',
  '🐝': '蜜蜂'
}

// 方法
const getEmojiTitle = (emoji) => {
  return emojiTitles[emoji] || emoji
}

const selectEmoji = (emoji) => {
  emit('update:modelValue', emoji)
  // 选择emoji后自动关闭弹窗
  if (popoverRef.value) {
    popoverRef.value.hide()
  }
}

const handleTriggerClick = () => {
  // 点击触发器时的处理
}

const closePopover = () => {
  if (popoverRef.value) {
    popoverRef.value.hide()
  }
}
</script>

<style scoped>
.emoji-picker {
  display: inline-block;
}

.emoji-trigger {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-width: 80px;
  height: 32px;
  padding: 0 8px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  background: white;
}

.emoji-trigger:hover {
  border-color: #409eff;
}

.current-emoji {
  font-size: 16px;
  line-height: 1;
}

.dropdown-icon {
  font-size: 12px;
  color: #c0c4cc;
  transition: transform 0.3s;
}

.emoji-picker-content {
  width: 100%;
}

.emoji-grid {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 4px;
  padding: 8px;
  max-height: 200px;
  overflow-y: auto;
}

.emoji-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  font-size: 18px;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s;
}

.emoji-item:hover {
  background-color: #f5f7fa;
  transform: scale(1.1);
}

.emoji-item.active {
  background-color: #409eff;
  color: white;
}

.emoji-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  padding: 8px;
  border-top: 1px solid #ebeef5;
}

:deep(.emoji-popover) {
  padding: 0 !important;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

:deep(.el-tabs__header) {
  margin: 0;
  background: #f8f9fa;
  border-radius: 8px 8px 0 0;
}

:deep(.el-tabs__nav-wrap) {
  padding: 8px 8px 0;
}

:deep(.el-tabs__content) {
  padding: 0;
}

:deep(.el-tab-pane) {
  padding: 0;
}

:deep(.el-tabs__item) {
  font-size: 12px;
  padding: 0 8px;
  height: 28px;
  line-height: 28px;
}

:deep(.el-tabs__item.is-active) {
  background: white;
  border-radius: 4px;
}
</style>
