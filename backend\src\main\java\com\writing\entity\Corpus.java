package com.writing.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 语料库实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("corpus")
public class Corpus {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    @TableField("novel_id")
    private Long novelId;
    
    @TableField("title")
    private String title;
    
    @TableField("type")
    private String type;
    
    @TableField("content")
    private String content;
    
    @TableField(value = "tags", typeHandler = JacksonTypeHandler.class)
    private List<String> tags;
    
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
    
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
    
    @TableLogic
    @TableField("deleted")
    private Integer deleted;
}
