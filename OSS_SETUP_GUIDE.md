# 阿里云OSS配置指南

## 概述

本项目已集成阿里云OSS对象存储服务来解决图片存储问题。原来的错误是因为数据库中的`cover`字段只有255个字符长度，无法存储base64编码的图片数据。现在通过OSS存储图片文件，数据库中只存储图片的URL链接。

## 配置步骤

### 1. 创建阿里云OSS Bucket

1. 登录阿里云控制台
2. 进入对象存储OSS服务
3. 创建新的Bucket：
   - 选择合适的地域（建议选择离用户最近的地域）
   - 设置Bucket名称（全局唯一）
   - 读写权限设置为"公共读"（允许匿名访问图片）
   - 其他设置保持默认

### 2. 获取访问密钥

1. 进入阿里云控制台的"访问控制RAM"
2. 创建新的用户或使用现有用户
3. 为用户添加OSS相关权限：
   - `AliyunOSSFullAccess`（完整权限）
   - 或者创建自定义权限策略，只授予必要的权限
4. 获取AccessKey ID和AccessKey Secret

### 3. 修改配置文件

编辑 `backend/src/main/resources/application.yml` 文件：

```yaml
# 阿里云OSS配置
aliyun:
  oss:
    endpoint: oss-cn-hangzhou.aliyuncs.com  # 替换为你的Bucket所在地域的Endpoint
    access-key-id: your-access-key-id      # 替换为实际的AccessKey ID
    access-key-secret: your-access-key-secret  # 替换为实际的AccessKey Secret
    bucket-name: your-bucket-name          # 替换为实际的Bucket名称
    url-prefix: https://your-bucket-name.oss-cn-hangzhou.aliyuncs.com/  # 替换为实际的访问域名

# 文件上传配置
file:
  upload:
    max-size: 10MB
    allowed-types: jpg,jpeg,png,gif,bmp,webp
```

### 4. 常见地域Endpoint

| 地域 | Endpoint |
|------|----------|
| 华东1（杭州） | oss-cn-hangzhou.aliyuncs.com |
| 华东2（上海） | oss-cn-shanghai.aliyuncs.com |
| 华北1（青岛） | oss-cn-qingdao.aliyuncs.com |
| 华北2（北京） | oss-cn-beijing.aliyuncs.com |
| 华南1（深圳） | oss-cn-shenzhen.aliyuncs.com |

## 功能说明

### 新增的API接口

1. **POST /api/upload/image** - 上传普通图片
2. **POST /api/upload/cover** - 上传小说封面
3. **POST /api/upload/document** - 上传文档文件
4. **DELETE /api/upload/file** - 删除文件

### 文件存储结构

```
your-bucket/
├── images/           # 普通图片
│   └── 2024/01/15/   # 按日期分组
│       └── uuid.jpg
├── documents/        # 文档文件
│   └── 2024/01/15/
│       └── uuid.pdf
```

### 自动清理机制

- 当更新小说封面时，会自动删除旧的封面文件
- 当删除小说时，会自动删除相关的封面文件

## 安全建议

1. **权限最小化**：只授予必要的OSS权限
2. **访问控制**：考虑设置Bucket的访问策略
3. **防盗链**：可以配置Referer防盗链
4. **HTTPS**：建议使用HTTPS访问

## 故障排除

### 常见错误

1. **AccessDenied**：检查AccessKey权限
2. **NoSuchBucket**：检查Bucket名称和地域
3. **InvalidAccessKeyId**：检查AccessKey ID是否正确
4. **SignatureDoesNotMatch**：检查AccessKey Secret是否正确

### 调试方法

1. 查看后端日志：`backend/logs/`
2. 检查网络连接
3. 验证配置参数

## 成本优化

1. **存储类型**：根据访问频率选择合适的存储类型
2. **生命周期**：设置自动删除过期文件的规则
3. **CDN加速**：对于高访问量，可以配置CDN

## 备份建议

1. 定期备份重要文件
2. 考虑跨地域复制
3. 版本控制（如需要）
