package com.writing.service;

import com.writing.config.FileUploadConfig;
import com.writing.util.OssUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.List;

/**
 * 文件上传服务
 */
@Slf4j
@Service
public class FileUploadService {
    
    @Autowired
    private OssUtil ossUtil;
    
    @Autowired
    private FileUploadConfig fileUploadConfig;
    
    /**
     * 上传图片文件
     * 
     * @param file 图片文件
     * @return 图片URL
     */
    public String uploadImage(MultipartFile file) {
        // 验证文件
        validateFile(file);
        
        // 验证文件类型
        validateImageType(file);
        
        // 上传到OSS
        return ossUtil.uploadFile(file, "images");
    }
    
    /**
     * 上传文档文件
     *
     * @param file 文档文件
     * @return 文档URL
     */
    public String uploadDocument(MultipartFile file) {
        // 验证文件
        validateFile(file);

        // 上传到OSS
        return ossUtil.uploadFile(file, "documents");
    }

    /**
     * 上传小说文件
     *
     * @param file 小说文件
     * @return 文档URL
     */
    public String uploadNovelFile(MultipartFile file) {
        // 验证文件
        validateFile(file);

        // 验证文件类型
        validateNovelFileType(file);

        // 上传到OSS
        return ossUtil.uploadFile(file, "novels");
    }
    
    /**
     * 删除文件
     * 
     * @param fileUrl 文件URL
     */
    public void deleteFile(String fileUrl) {
        ossUtil.deleteFile(fileUrl);
    }
    
    /**
     * 验证文件基本信息
     * 
     * @param file 文件
     */
    private void validateFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new RuntimeException("文件不能为空");
        }
        
        // 检查文件大小
        if (file.getSize() > fileUploadConfig.getMaxSizeInBytes()) {
            throw new RuntimeException("文件大小超过限制: " + fileUploadConfig.getMaxSize());
        }
    }
    
    /**
     * 验证图片文件类型
     * 
     * @param file 文件
     */
    private void validateImageType(MultipartFile file) {
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null) {
            throw new RuntimeException("文件名不能为空");
        }
        
        // 获取文件扩展名
        String extension = "";
        if (originalFilename.contains(".")) {
            extension = originalFilename.substring(originalFilename.lastIndexOf(".") + 1).toLowerCase();
        }
        
        // 检查是否为允许的图片类型
        List<String> allowedTypes = fileUploadConfig.getAllowedTypes();
        if (allowedTypes != null && !allowedTypes.contains(extension)) {
            throw new RuntimeException("不支持的文件类型: " + extension + "，支持的类型: " + String.join(", ", allowedTypes));
        }
        
        // 检查MIME类型
        String contentType = file.getContentType();
        if (contentType == null || !contentType.startsWith("image/")) {
            throw new RuntimeException("文件不是有效的图片格式");
        }
    }

    /**
     * 验证小说文件类型
     *
     * @param file 文件
     */
    private void validateNovelFileType(MultipartFile file) {
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null) {
            throw new RuntimeException("文件名不能为空");
        }

        // 获取文件扩展名
        String extension = "";
        if (originalFilename.contains(".")) {
            extension = originalFilename.substring(originalFilename.lastIndexOf(".") + 1).toLowerCase();
        }

        // 检查是否为txt文件
        if (!"txt".equals(extension)) {
            throw new RuntimeException("只支持txt格式的小说文件");
        }

        // 检查MIME类型
        String contentType = file.getContentType();
        if (contentType != null && !contentType.equals("text/plain") && !contentType.equals("application/octet-stream")) {
            // 允许text/plain和application/octet-stream（某些浏览器上传txt文件时的MIME类型）
            log.warn("小说文件MIME类型可能不正确: {}, 但仍然允许上传", contentType);
        }
    }
}
