import apiClient from './apiClient'

/**
 * 认证相关API
 */
export const authApi = {
  
  /**
   * 用户登录
   */
  login(username, password) {
    return apiClient.post('/auth/login', {
      username,
      password
    })
  },
  
  /**
   * 用户注册
   */
  register(username, email, password) {
    return apiClient.post('/auth/register', {
      username,
      email,
      password
    })
  },
  
  /**
   * 获取当前用户信息
   */
  getCurrentUser() {
    return apiClient.get('/auth/me')
  },

  /**
   * 更新用户信息
   */
  updateProfile(profileData) {
    return apiClient.put('/auth/profile', profileData)
  },

  /**
   * 退出登录
   */
  logout() {
    // 清除本地存储的token和用户信息
    localStorage.removeItem('token')
    localStorage.removeItem('user')
    return Promise.resolve()
  }
}

/**
 * 认证工具函数
 */
export const authUtils = {

  /**
   * 保存登录信息
   */
  saveAuthData(token, user) {
    localStorage.setItem('token', token)
    localStorage.setItem('user', JSON.stringify(user))
    // 保存登录时间，用于token过期检查
    localStorage.setItem('loginTime', Date.now().toString())
  },

  /**
   * 获取token
   */
  getToken() {
    return localStorage.getItem('token')
  },

  /**
   * 获取用户信息
   */
  getUser() {
    const userStr = localStorage.getItem('user')
    return userStr ? JSON.parse(userStr) : null
  },

  /**
   * 检查token是否过期（前端简单检查，7天过期）
   */
  isTokenExpired() {
    const loginTime = localStorage.getItem('loginTime')
    if (!loginTime) return true

    const now = Date.now()
    const loginTimestamp = parseInt(loginTime)
    const expireTime = 7 * 24 * 60 * 60 * 1000 // 7天

    return (now - loginTimestamp) > expireTime
  },

  /**
   * 检查是否已登录且token有效
   */
  isLoggedIn() {
    const token = this.getToken()
    if (!token) return false

    // 检查token是否过期
    if (this.isTokenExpired()) {
      this.clearAuthData()
      return false
    }

    return true
  },

  /**
   * 验证token有效性（通过API调用）
   */
  async validateToken() {
    try {
      await authApi.getCurrentUser()
      return true
    } catch (error) {
      // token无效，清除认证信息
      this.clearAuthData()
      return false
    }
  },

  /**
   * 清除认证信息
   */
  clearAuthData() {
    localStorage.removeItem('token')
    localStorage.removeItem('user')
    localStorage.removeItem('loginTime')
  },

  /**
   * 强制跳转到登录页
   */
  redirectToLogin(message = '请重新登录') {
    this.clearAuthData()

    // 如果在Vue Router环境中
    if (window.$router) {
      window.$router.push('/login')
    } else {
      // 直接使用window.location
      window.location.href = '/login'
    }

    // 显示提示信息
    if (window.$message) {
      window.$message.warning(message)
    }
  }
}
