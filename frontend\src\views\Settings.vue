<template>
  <div class="settings-page">
    <div class="settings-header">
      <h2>🔑 AI模型API配置</h2>
      <el-button type="primary" @click="testAllConnections">测试所有连接</el-button>
    </div>

    <ApiConfig />
  </div>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import ApiConfig from '@/components/ApiConfig.vue'
import { apiConfigApi } from '@/services/apiConfigApi.js'

// 方法
const testAllConnections = async () => {
  try {
    ElMessage.info('正在获取API配置列表...')
    const configs = await apiConfigApi.getApiConfigs()
    const enabledConfigs = configs.filter(config => config.enabled && config.apiKey)

    if (enabledConfigs.length === 0) {
      ElMessage.warning('没有可测试的配置')
      return
    }

    ElMessage.info(`正在测试 ${enabledConfigs.length} 个配置的连接...`)

    let successCount = 0
    let failCount = 0

    for (const config of enabledConfigs) {
      try {
        await apiConfigApi.testApiConfig(config.id)
        successCount++
        ElMessage.success(`${config.name} 连接成功`)
      } catch (error) {
        failCount++
        ElMessage.error(`${config.name} 连接失败`)
      }
      // 间隔500ms避免请求过于频繁
      await new Promise(resolve => setTimeout(resolve, 500))
    }

    ElMessage.info(`测试完成：${successCount} 个成功，${failCount} 个失败`)
  } catch (error) {
    console.error('测试连接失败:', error)
    ElMessage.error('测试连接失败')
  }
}
</script>

<style scoped>
.settings-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.settings-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}


/* 主题样式 */
:root[data-theme="light"] {
  --bg-color: #ffffff;
  --text-color: #303133;
  --border-color: #e4e7ed;
}

:root[data-theme="dark"] {
  --bg-color: #1d1d1d;
  --text-color: #ffffff;
  --border-color: #434343;
}

:root[data-theme="dark"] .settings-page {
  background-color: var(--bg-color);
  color: var(--text-color);
}

:root[data-theme="dark"] .el-card {
  background-color: #2d2d2d;
  border-color: var(--border-color);
}

/* 禁用动画 */
.no-animations * {
  animation-duration: 0ms !important;
  animation-delay: 0ms !important;
  transition-duration: 0ms !important;
  transition-delay: 0ms !important;
}
</style>