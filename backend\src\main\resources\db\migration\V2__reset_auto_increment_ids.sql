-- 重置所有表的自增ID，确保使用简短的数字ID

-- 清空所有表数据（如果有的话）
SET FOREIGN_KEY_CHECKS = 0;

TRUNCATE TABLE `writing_records`;
TRUNCATE TABLE `announcement_reads`;
TRUNCATE TABLE `token_usage_stats`;
TRUNCATE TABLE `billing_records`;
TRUNCATE TABLE `account_balance`;
TRUNCATE TABLE `api_configs`;
TRUNCATE TABLE `novel_genres`;
TRUNCATE TABLE `writing_goals`;
TRUNCATE TABLE `prompts`;
TRUNCATE TABLE `events`;
TRUNCATE TABLE `corpus`;
TRUNCATE TABLE `world_settings`;
TRUNCATE TABLE `characters`;
TRUNCATE TABLE `chapters`;
TRUNCATE TABLE `novels`;
TRUNCATE TABLE `users`;

SET FOREIGN_KEY_CHECKS = 1;

-- 重置自增ID起始值
ALTER TABLE `users` AUTO_INCREMENT = 1;
ALTER TABLE `novels` AUTO_INCREMENT = 1;
ALTER TABLE `chapters` AUTO_INCREMENT = 1;
ALTER TABLE `characters` AUTO_INCREMENT = 1;
ALTER TABLE `world_settings` AUTO_INCREMENT = 1;
ALTER TABLE `corpus` AUTO_INCREMENT = 1;
ALTER TABLE `events` AUTO_INCREMENT = 1;
ALTER TABLE `prompts` AUTO_INCREMENT = 1;
ALTER TABLE `writing_goals` AUTO_INCREMENT = 1;
ALTER TABLE `novel_genres` AUTO_INCREMENT = 1;
ALTER TABLE `api_configs` AUTO_INCREMENT = 1;
ALTER TABLE `account_balance` AUTO_INCREMENT = 1;
ALTER TABLE `billing_records` AUTO_INCREMENT = 1;
ALTER TABLE `token_usage_stats` AUTO_INCREMENT = 1;
ALTER TABLE `writing_records` AUTO_INCREMENT = 1;
ALTER TABLE `announcement_reads` AUTO_INCREMENT = 1;
