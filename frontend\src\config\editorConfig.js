// WangEditor 编辑器配置文件
// 统一管理编辑器的工具栏和配置选项

// 完整的写作工具栏配置（用于主要的写作界面）
export const fullWritingToolbarConfig = {
  toolbarKeys: [
    // 基础格式
    'bold', 'italic', 'underline', 'through',
    '|',
    // 字体和颜色
    'fontSize', 'color', 'bgColor',
    '|',
    // 段落格式
    'headerSelect', 'lineHeight', 'justifyLeft', 'justifyCenter', 'justifyRight',
    '|',
    // 列表和引用
    'bulletedList', 'numberedList', 'blockquote',
    '|',
    // 插入功能组
    {
      key: 'group-insert',
      title: '插入',
      menuKeys: ['insertLink', 'insertImage', 'emotion', 'divider']
    },
    '|',
    // 编辑操作
    'undo', 'redo', 'clearStyle',
    '|'
  ],
  // 排除不需要的功能
  excludeKeys: [
    'sub', 'sup', 'code', 'codeBlock', 'fontFamily',
    'insertTable', 'insertVideo', 'uploadVideo'
  ]
}

// 简化的工具栏配置（用于预览或简单编辑）
export const simpleToolbarConfig = {
  toolbarKeys: [
    // 基础格式
    'bold', 'italic', 'underline',
    '|',
    // 字体和颜色
    'fontSize', 'color',
    '|',
    // 段落格式
    'headerSelect', 'lineHeight', 'justifyLeft', 'justifyCenter', 'justifyRight',
    '|',
    // 列表
    'bulletedList', 'numberedList',
    '|',
    // 编辑操作
    'undo', 'redo', 'clearStyle'
  ]
}

// 基础编辑器配置
export const baseEditorConfig = {
  MENU_CONF: {
    uploadImage: {
      server: '/api/upload-image',
      fieldName: 'file',
      maxFileSize: 5 * 1024 * 1024,
      allowedFileTypes: ['image/*']
    },
    // 字体大小配置
    fontSize: {
      fontSizeList: ['12px', '14px', '16px', '18px', '20px', '24px', '28px', '32px']
    },
    // 行高配置
    lineHeight: {
      lineHeightList: ['1', '1.15', '1.5', '1.75', '2', '2.5', '3']
    },
    // 颜色配置
    color: {
      colors: [
        '#000000', '#333333', '#666666', '#999999',
        '#cc0000', '#ff6600', '#ffcc00', '#009900',
        '#0066cc', '#6600cc', '#cc0066', '#ffffff'
      ]
    }
  }
}

// 创建完整的编辑器配置
export function createEditorConfig(placeholder = '开始您的创作...') {
  return {
    placeholder,
    ...baseEditorConfig
  }
}

// 创建简化的编辑器配置
export function createSimpleEditorConfig(placeholder = '请输入内容...') {
  return {
    placeholder,
    MENU_CONF: {
      uploadImage: baseEditorConfig.MENU_CONF.uploadImage,
      fontSize: {
        fontSizeList: ['12px', '14px', '16px', '18px', '20px', '24px']
      },
      lineHeight: {
        lineHeightList: ['1', '1.15', '1.5', '1.75', '2', '2.5']
      }
    }
  }
}

// 预设配置组合
export const editorPresets = {
  // 主写作界面
  writer: {
    toolbar: fullWritingToolbarConfig,
    editor: createEditorConfig('开始您的创作...')
  },
  // 首页编辑器
  home: {
    toolbar: simpleToolbarConfig,
    editor: createSimpleEditorConfig('在这里编辑您的小说内容...')
  },
  // 短篇小说编辑器
  shortStory: {
    toolbar: simpleToolbarConfig,
    editor: createSimpleEditorConfig('生成的小说内容将显示在这里...')
  },
  // 短文编辑器
  article: {
    toolbar: simpleToolbarConfig,
    editor: createSimpleEditorConfig('生成的短文内容将显示在这里，您也可以直接编辑...')
  }
}
