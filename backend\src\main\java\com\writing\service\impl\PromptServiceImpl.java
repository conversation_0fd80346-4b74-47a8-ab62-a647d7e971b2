package com.writing.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.writing.entity.Prompt;
import com.writing.mapper.PromptMapper;
import com.writing.service.PromptService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 提示词服务实现类
 */
@Service
@RequiredArgsConstructor
public class PromptServiceImpl extends ServiceImpl<PromptMapper, Prompt> implements PromptService {
    
    @Override
    public List<Prompt> getPromptsByUserId(Long userId, String category) {
        LambdaQueryWrapper<Prompt> queryWrapper = new LambdaQueryWrapper<Prompt>()
                .eq(Prompt::getUserId, userId)
                .orderByDesc(Prompt::getUsageCount)
                .orderByDesc(Prompt::getUpdatedAt);
        
        // 如果指定了分类，添加分类筛选
        if (StringUtils.hasText(category)) {
            queryWrapper.eq(Prompt::getCategory, category);
        }
        
        return this.list(queryWrapper);
    }
    
    @Override
    public Prompt getPromptById(Long promptId, Long userId) {
        return this.getOne(new LambdaQueryWrapper<Prompt>()
                .eq(Prompt::getId, promptId)
                .eq(Prompt::getUserId, userId));
    }
    
    @Override
    public Prompt createPrompt(Prompt prompt) {
        // 设置默认值
        if (prompt.getIsDefault() == null) {
            prompt.setIsDefault(0); // 0表示非默认，1表示默认
        }
        if (prompt.getUsageCount() == null) {
            prompt.setUsageCount(0);
        }
        
        this.save(prompt);
        return prompt;
    }
    
    @Override
    public Prompt updatePrompt(Prompt prompt, Long userId) {
        // 验证提示词是否存在且属于当前用户
        Prompt existingPrompt = this.getOne(new LambdaQueryWrapper<Prompt>()
                .eq(Prompt::getId, prompt.getId())
                .eq(Prompt::getUserId, userId));
        
        if (existingPrompt == null) {
            throw new RuntimeException("提示词不存在或无权限修改");
        }
        
        // 保留原有的使用次数和默认状态
        prompt.setUsageCount(existingPrompt.getUsageCount());
        if (prompt.getIsDefault() == null) {
            prompt.setIsDefault(existingPrompt.getIsDefault());
        }
        
        this.updateById(prompt);
        return prompt;
    }
    
    @Override
    public boolean deletePrompt(Long promptId, Long userId) {
        // 验证提示词是否存在且属于当前用户
        Prompt prompt = this.getOne(new LambdaQueryWrapper<Prompt>()
                .eq(Prompt::getId, promptId)
                .eq(Prompt::getUserId, userId));
        
        if (prompt == null) {
            throw new RuntimeException("提示词不存在或无权限删除");
        }
        
        // 不允许删除默认提示词
        if (prompt.getIsDefault() != null && prompt.getIsDefault() == 1) {
            throw new RuntimeException("不能删除默认提示词");
        }
        
        return this.removeById(promptId);
    }
    
    @Override
    public void incrementUsageCount(Long promptId, Long userId) {
        // 验证提示词是否存在且属于当前用户
        Prompt prompt = this.getOne(new LambdaQueryWrapper<Prompt>()
                .eq(Prompt::getId, promptId)
                .eq(Prompt::getUserId, userId));
        
        if (prompt == null) {
            throw new RuntimeException("提示词不存在或无权限访问");
        }
        
        // 增加使用次数
        prompt.setUsageCount(prompt.getUsageCount() + 1);
        this.updateById(prompt);
    }
    
    @Override
    public void initDefaultPrompts(Long userId) {
        // 检查是否已经初始化过
        long count = this.count(new LambdaQueryWrapper<Prompt>()
                .eq(Prompt::getUserId, userId)
                .eq(Prompt::getIsDefault, 1));

        if (count > 0) {
            return; // 已经初始化过
        }

        // 创建默认提示词
        List<Prompt> defaultPrompts = getDefaultPrompts();
        for (Prompt prompt : defaultPrompts) {
            prompt.setUserId(userId);
            this.save(prompt);
        }
    }

    @Override
    public List<Prompt> getDefaultPrompts() {
        List<Prompt> defaultPrompts = new ArrayList<>();

        // 1. 小说大纲生成器
        Prompt outlineGenerator = new Prompt();
        outlineGenerator.setTitle("小说大纲生成器");
        outlineGenerator.setCategory("outline");
        outlineGenerator.setDescription("根据关键词和类型生成详细的小说大纲");
        outlineGenerator.setContent("请为我创作一个{类型}小说的大纲，主题是{主题}，主角是{主角设定}。要求包含：\n1. 故事背景设定\n2. 主要人物介绍\n3. 核心冲突\n4. 章节大纲（至少10章）\n5. 结局走向");
        outlineGenerator.setTags(Arrays.asList("大纲", "结构", "创作"));
        outlineGenerator.setIsDefault(1);
        outlineGenerator.setUsageCount(0);
        defaultPrompts.add(outlineGenerator);

        // 2. 基础章节生成器
        Prompt basicChapterGenerator = new Prompt();
        basicChapterGenerator.setTitle("基础章节生成器");
        basicChapterGenerator.setCategory("content");
        basicChapterGenerator.setDescription("基于章节大纲生成详细的正文内容");
        basicChapterGenerator.setContent("请为小说《{小说标题}》的章节《{章节标题}》写正文内容。\n\n章节大纲：{章节大纲}\n\n要求：\n1. 字数控制在{目标字数}字左右\n2. 采用{写作视角}视角\n3. 包含丰富的对话、描写和细节\n4. 保持情节连贯性\n5. 突出{重点内容}");
        basicChapterGenerator.setTags(Arrays.asList("正文", "章节", "基础生成"));
        basicChapterGenerator.setIsDefault(1);
        basicChapterGenerator.setUsageCount(0);
        defaultPrompts.add(basicChapterGenerator);

        // 3. 文本润色优化
        Prompt textPolisher = new Prompt();
        textPolisher.setTitle("文本润色优化");
        textPolisher.setCategory("polish");
        textPolisher.setDescription("优化文本的表达和文采，提升阅读体验");
        textPolisher.setContent("请帮我润色以下文本，要求：\n1. 保持原意不变\n2. 提升文采和表达力\n3. 优化句式结构\n4. 增强画面感\n\n原文：{原文内容}");
        textPolisher.setTags(Arrays.asList("润色", "优化", "文采"));
        textPolisher.setIsDefault(1);
        textPolisher.setUsageCount(0);
        defaultPrompts.add(textPolisher);

        // 4. 智能续写助手
        Prompt continueWriter = new Prompt();
        continueWriter.setTitle("智能续写助手");
        continueWriter.setCategory("continue");
        continueWriter.setDescription("基于现有内容进行智能续写");
        continueWriter.setContent("请为小说《{小说标题}》的章节《{章节标题}》续写内容。\n\n当前已写内容：\n{当前内容}\n\n续写要求：\n1. 保持原有风格和语调\n2. 情节自然连贯\n3. 长度约{续写字数}字\n4. 推进剧情发展");
        continueWriter.setTags(Arrays.asList("续写", "连贯", "发展"));
        continueWriter.setIsDefault(1);
        continueWriter.setUsageCount(0);
        defaultPrompts.add(continueWriter);

        // 5. 人物设定生成器
        Prompt characterGenerator = new Prompt();
        characterGenerator.setTitle("人物设定生成器");
        characterGenerator.setCategory("character");
        characterGenerator.setDescription("生成详细的人物设定和背景故事");
        characterGenerator.setContent("请为小说《{小说标题}》创建一个{角色类型}角色，基本信息：\n- 姓名：{姓名}\n- 角色定位：{角色定位}\n- 性别：{性别}\n- 年龄：{年龄}岁\n\n请详细设定：\n1. 外貌特征\n2. 性格特点\n3. 背景故事\n4. 能力特长\n5. 人际关系\n6. 内心动机");
        characterGenerator.setTags(Arrays.asList("人设", "角色", "背景"));
        characterGenerator.setIsDefault(1);
        characterGenerator.setUsageCount(0);
        defaultPrompts.add(characterGenerator);

        // 6. 全素材章节生成器
        Prompt fullMaterialGenerator = new Prompt();
        fullMaterialGenerator.setTitle("全素材章节生成器");
        fullMaterialGenerator.setCategory("content");
        fullMaterialGenerator.setDescription("结合人物、世界观、语料库等素材生成章节内容");
        fullMaterialGenerator.setContent("请为小说《{小说标题}》的章节《{章节标题}》写正文内容。\n\n章节大纲：{章节大纲}\n\n{主要人物}\n\n{世界观设定}\n\n{参考语料}\n\n{前文概要}\n\n创作要求：\n1. 字数控制在{目标字数}字左右\n2. 采用{写作视角}视角\n3. 突出重点：{重点内容}\n4. 充分运用提供的人物设定和世界观背景\n5. 参考语料库的写作风格和表达方式\n6. 与前文保持连贯性和一致性\n7. 包含丰富的对话、心理活动、环境描写\n8. 情节发展要符合章节大纲要求");
        fullMaterialGenerator.setTags(Arrays.asList("全素材", "章节", "综合生成"));
        fullMaterialGenerator.setIsDefault(1);
        fullMaterialGenerator.setUsageCount(0);
        defaultPrompts.add(fullMaterialGenerator);

        // 7. 对话驱动生成器
        Prompt dialogueGenerator = new Prompt();
        dialogueGenerator.setTitle("对话驱动生成器");
        dialogueGenerator.setCategory("content-dialogue");
        dialogueGenerator.setDescription("以对话为主导的章节内容生成");
        dialogueGenerator.setContent("请为小说《{小说标题}》的章节《{章节标题}》写正文内容，重点突出对话。\n\n章节大纲：{章节大纲}\n参与对话人物：{主要人物}\n\n创作要求：\n1. 字数控制在{目标字数}字左右\n2. 对话占60%以上篇幅\n3. 通过对话推进情节发展\n4. 展现人物性格和关系\n5. 适当加入动作和心理描写\n6. 对话要符合人物身份和性格\n7. 重点内容：{重点内容}");
        dialogueGenerator.setTags(Arrays.asList("对话", "人物", "互动"));
        dialogueGenerator.setIsDefault(1);
        dialogueGenerator.setUsageCount(0);
        defaultPrompts.add(dialogueGenerator);

        // 8. 场景描写生成器
        Prompt sceneGenerator = new Prompt();
        sceneGenerator.setTitle("场景描写生成器");
        sceneGenerator.setCategory("content-scene");
        sceneGenerator.setDescription("以环境和场景描写为主的内容生成");
        sceneGenerator.setContent("请为小说《{小说标题}》的章节《{章节标题}》写正文内容，重点突出场景描写。\n\n章节大纲：{章节大纲}\n场景设定：{世界观设定}\n\n创作要求：\n1. 字数控制在{目标字数}字左右\n2. 详细描写环境氛围\n3. 通过场景烘托情节\n4. 调动读者五感体验\n5. 场景与情节相辅相成\n6. 体现世界观特色\n7. 重点内容：{重点内容}");
        sceneGenerator.setTags(Arrays.asList("场景", "环境", "氛围"));
        sceneGenerator.setIsDefault(1);
        sceneGenerator.setUsageCount(0);
        defaultPrompts.add(sceneGenerator);

        // 9. 动作剧情生成器
        Prompt actionGenerator = new Prompt();
        actionGenerator.setTitle("动作剧情生成器");
        actionGenerator.setCategory("content-action");
        actionGenerator.setDescription("以动作和情节推进为主的内容生成");
        actionGenerator.setContent("请为小说《{小说标题}》的章节《{章节标题}》写正文内容，重点突出动作情节。\n\n章节大纲：{章节大纲}\n主要人物：{主要人物}\n\n创作要求：\n1. 字数控制在{目标字数}字左右\n2. 节奏紧凑，情节推进迅速\n3. 动作描写清晰流畅\n4. 突出冲突和转折\n5. 保持紧张感和悬念\n6. 角色行动符合性格\n7. 重点内容：{重点内容}");
        actionGenerator.setTags(Arrays.asList("动作", "情节", "冲突"));
        actionGenerator.setIsDefault(1);
        actionGenerator.setUsageCount(0);
        defaultPrompts.add(actionGenerator);

        // 10. 心理描写生成器
        Prompt psychologyGenerator = new Prompt();
        psychologyGenerator.setTitle("心理描写生成器");
        psychologyGenerator.setCategory("content-psychology");
        psychologyGenerator.setDescription("以心理活动和内心独白为主的内容生成");
        psychologyGenerator.setContent("请为小说《{小说标题}》的章节《{章节标题}》写正文内容，重点突出心理描写。\n\n章节大纲：{章节大纲}\n主角心境：{重点内容}\n人物背景：{主要人物}\n\n创作要求：\n1. 字数控制在{目标字数}字左右\n2. 深入挖掘人物内心世界\n3. 心理活动要真实细腻\n4. 体现人物成长变化\n5. 内心冲突与外在情节呼应\n6. 适当运用意识流技巧\n7. 展现人物独特思维方式");
        psychologyGenerator.setTags(Arrays.asList("心理", "内心", "情感"));
        psychologyGenerator.setIsDefault(1);
        psychologyGenerator.setUsageCount(0);
        defaultPrompts.add(psychologyGenerator);

        // 11. 科幻修仙世界观生成器
        Prompt sciFiCultivationWorldGenerator = new Prompt();
        sciFiCultivationWorldGenerator.setTitle("科幻修仙世界观生成器");
        sciFiCultivationWorldGenerator.setCategory("worldview");
        sciFiCultivationWorldGenerator.setDescription("专门用于科幻修仙类小说的世界观创作，融合现代科技与传统修真");
        sciFiCultivationWorldGenerator.setContent("【科幻修仙世界观解析框架】\n\n===== 核心设定 =====\n世界类型：科幻修仙融合世界\n时间背景：{时间背景}（如：2157年，修真复苏第200年）\n技术水平：{科技水平}（如：纳米科技+量子计算+灵力工程）\n修真体系：{修真体系}（如：数字化修真、脑机接口修仙、算法炼丹）\n\n===== 社会结构 =====\n政治制度：{政治制度}（如：修真公司联盟制、灵力民主制）\n经济模式：{经济模式}（如：香火算力交易、因果数据经济）\n阶级分层：{阶级分层}（如：码农修士、产品经理仙君、AI渡劫者）\n文化特色：{文化特色}（如：赛博朋克修真文化、元宇宙仙侠）\n\n===== 特殊机制 =====\n独特法则：{独特法则}\n- 机械佛莲因果服务器：直径十万公里，解析众生因果数据流\n- 人造流星雨元神回收：每百年佛莲绽放，喷射渡劫失败修士元神\n- 灵力带宽战争：传统灵脉vs脑机接口Wi-Fi灵力\n- 香火数据化：祈祷转化为区块链符咒\n- 天劫供电协议：雷劫导入城市电网换取免税\n- 赛博心魔广告：修行时弹出心魔广告窗口\n- 电子孟婆汤：选择性删除情劫记忆\n\n限制条件：{限制条件}\n冲突矛盾：{冲突矛盾}（如：传统修真门派vs科技修仙者）\n\n===== 关键元素 =====\n重要设施：{重要设施}（如：轮回科技公司、玄霄5G基站、赛博神龛）\n特殊物品：{特殊物品}（如：业力API接口、九霄雷劫调度合同、因果重构器）\n势力组织：{势力组织}（如：修真门派、科技公司、轮回集团）\n\n===== 故事背景 =====\n主要冲突：{主要冲突}\n时代特征：{时代特征}\n\n【创作要求】\n请基于以上科幻修仙世界观设定，创作一个{故事类型}故事，主角是{主角设定}，情节围绕{核心情节}展开。\n\n【风格要求】\n1. 融合现代科技术语与传统修真概念\n2. 体现科技与修真的创新结合点\n3. 保持世界观的内在逻辑一致性\n4. 突出人物在新时代背景下的适应与冲突\n5. 展现传统文化在科技时代的传承与变革\n\n【输出格式】\n## 世界观核心\n{总结世界观的核心特色}\n\n## 故事内容\n{主要情节内容，融入世界观元素}\n\n## 科技修真元素运用\n- 技术设定：{具体的科技修真设定}\n- 冲突矛盾：{科技与传统的碰撞}\n- 创新点：{独特的世界观创新}\n\n## 后续发展提示\n{为情节延续提供的世界观支撑}\n\n---\n请确保所有描写都严格遵循科幻修仙的世界观设定，体现传统修真与现代科技的深度融合。");
        sciFiCultivationWorldGenerator.setTags(Arrays.asList("科幻修仙", "世界观", "融合设定", "因果系统", "机械佛莲"));
        sciFiCultivationWorldGenerator.setIsDefault(1);
        sciFiCultivationWorldGenerator.setUsageCount(0);
        defaultPrompts.add(sciFiCultivationWorldGenerator);

        // 12. 世界观强制解析模板
        Prompt worldviewForceParseTemplate = new Prompt();
        worldviewForceParseTemplate.setTitle("世界观强制解析模板");
        worldviewForceParseTemplate.setCategory("worldview");
        worldviewForceParseTemplate.setDescription("通用的世界观强制解析格式，确保AI正确理解复杂设定");
        worldviewForceParseTemplate.setContent("【重要提醒：请严格按照以下世界观设定进行创作】\n\n====================\n【世界观核心框架】\n====================\n\n{在此处详细描述您的世界观设定}\n\n====================\n【强制遵循规则】\n====================\n1. 任何情节发展必须符合上述世界观的内在逻辑\n2. 所有角色行为需要考虑世界观背景的影响\n3. 技术、魔法、社会制度等设定不可随意修改\n4. 创作过程中如遇冲突，优先遵循世界观设定\n5. 保持设定的一致性和连贯性\n\n====================\n【输出检查要求】\n====================\n在输出内容前，请自检：\n□ 是否遵循了世界观的基本法则？\n□ 角色行为是否符合世界背景？\n□ 情节发展是否有违世界观逻辑？\n□ 专有名词使用是否准确？\n\n====================\n【创作内容】\n====================\n基于以上世界观，请创作：\n\n标题：{标题}\n类型：{内容类型}\n要求：{具体要求}\n字数：{目标字数}\n\n【固定输出格式】\n## 标题\n{生成标题}\n\n## 正文内容\n{严格按照世界观设定创作的内容}\n\n## 世界观符合性检查\n- 设定遵循度：{评估是否完全遵循世界观}\n- 逻辑一致性：{检查内容逻辑是否与设定冲突}\n- 专有概念使用：{确认专有名词和概念使用准确}\n\n## 后续发展建议\n{基于世界观为后续情节提供的发展方向}\n\n---\n【最终确认】\n请确保以上内容100%符合既定世界观，绝不偏离设定框架。");
        worldviewForceParseTemplate.setTags(Arrays.asList("世界观", "强制解析", "格式化", "逻辑检查"));
        worldviewForceParseTemplate.setIsDefault(1);
        worldviewForceParseTemplate.setUsageCount(0);
        defaultPrompts.add(worldviewForceParseTemplate);

        // 13. 都市短篇小说生成器
        Prompt urbanShortStoryGenerator = new Prompt();
        urbanShortStoryGenerator.setTitle("都市短篇小说生成器");
        urbanShortStoryGenerator.setCategory("short-story");
        urbanShortStoryGenerator.setDescription("专门用于创作都市背景的短篇小说，贴近现代生活");
        urbanShortStoryGenerator.setContent("请创作一篇都市背景的短篇小说。\n\n【基础设定】\n- 小说标题：{小说标题}\n- 主角姓名：{主角姓名}（{主角性别}，{主角年龄}岁）\n- 故事地点：{故事地点}\n- 字数要求：{字数要求}\n\n【题材风格】\n题材类型：{题材类型}\n情节类型：{情节类型}\n情绪氛围：{情绪氛围}\n时间背景：{时间背景}\n\n【创作要求】\n{创作要求}\n\n【参考文本】\n{参考文本}\n\n【输出要求】\n1. 情节完整，有明确的开头、发展、高潮、结局\n2. 人物性格鲜明，符合都市背景设定\n3. 语言生动流畅，贴近现代生活\n4. 场景描写真实，体现都市特色\n5. 包含丰富的对话和细节描写\n6. 传达积极正面的价值观\n\n请创作一篇完整的都市短篇小说。");
        urbanShortStoryGenerator.setTags(Arrays.asList("短篇小说", "都市", "现代生活", "完整故事"));
        urbanShortStoryGenerator.setIsDefault(1);
        urbanShortStoryGenerator.setUsageCount(0);
        defaultPrompts.add(urbanShortStoryGenerator);

        // 14. 玄幻短篇小说生成器
        Prompt fantasyShortStoryGenerator = new Prompt();
        fantasyShortStoryGenerator.setTitle("玄幻短篇小说生成器");
        fantasyShortStoryGenerator.setCategory("short-story");
        fantasyShortStoryGenerator.setDescription("创作充满想象力的玄幻类短篇小说");
        fantasyShortStoryGenerator.setContent("请创作一篇玄幻背景的短篇小说。\n\n【基础设定】\n- 小说标题：{小说标题}\n- 主角姓名：{主角姓名}（{主角性别}，{主角年龄}岁）\n- 故事地点：{故事地点}\n- 字数要求：{字数要求}\n\n【玄幻元素】\n题材类型：{题材类型}\n情节类型：{情节类型}\n情绪氛围：{情绪氛围}\n时间背景：{时间背景}\n\n【创作要求】\n{创作要求}\n\n【参考文本】\n{参考文本}\n\n【输出要求】\n1. 构建完整的玄幻世界观背景\n2. 设计独特的修炼体系或魔法系统\n3. 情节紧凑，悬念迭起\n4. 人物具有鲜明的玄幻特色\n5. 包含精彩的战斗或法术描写\n6. 语言富有古典韵味或奇幻色彩\n7. 传达成长、正义等正面主题\n\n请创作一篇完整的玄幻短篇小说。");
        fantasyShortStoryGenerator.setTags(Arrays.asList("短篇小说", "玄幻", "修炼", "魔法", "完整故事"));
        fantasyShortStoryGenerator.setIsDefault(1);
        fantasyShortStoryGenerator.setUsageCount(0);
        defaultPrompts.add(fantasyShortStoryGenerator);

        // 15. 言情短篇小说生成器
        Prompt romanceShortStoryGenerator = new Prompt();
        romanceShortStoryGenerator.setTitle("言情短篇小说生成器");
        romanceShortStoryGenerator.setCategory("short-story");
        romanceShortStoryGenerator.setDescription("创作温馨动人的言情类短篇小说");
        romanceShortStoryGenerator.setContent("请创作一篇言情背景的短篇小说。\n\n【基础设定】\n- 小说标题：{小说标题}\n- 主角姓名：{主角姓名}（{主角性别}，{主角年龄}岁）\n- 故事地点：{故事地点}\n- 字数要求：{字数要求}\n\n【言情元素】\n题材类型：{题材类型}\n情节类型：{情节类型}\n情绪氛围：{情绪氛围}\n时间背景：{时间背景}\n\n【创作要求】\n{创作要求}\n\n【参考文本】\n{参考文本}\n\n【输出要求】\n1. 情感线索清晰，感情发展自然\n2. 男女主角性格互补，有化学反应\n3. 包含甜蜜互动和情感冲突\n4. 细节描写细腻，情感真挚动人\n5. 对话生动，体现人物性格\n6. 场景浪漫，氛围温馨\n7. 结局温暖，传递爱情的美好\n\n请创作一篇完整的言情短篇小说。");
        romanceShortStoryGenerator.setTags(Arrays.asList("短篇小说", "言情", "爱情", "甜蜜", "完整故事"));
        romanceShortStoryGenerator.setIsDefault(1);
        romanceShortStoryGenerator.setUsageCount(0);
        defaultPrompts.add(romanceShortStoryGenerator);

        // 16. 悬疑短篇小说生成器
        Prompt suspenseShortStoryGenerator = new Prompt();
        suspenseShortStoryGenerator.setTitle("悬疑短篇小说生成器");
        suspenseShortStoryGenerator.setCategory("short-story");
        suspenseShortStoryGenerator.setDescription("创作紧张刺激的悬疑推理类短篇小说");
        suspenseShortStoryGenerator.setContent("请创作一篇悬疑推理背景的短篇小说。\n\n【基础设定】\n- 小说标题：{小说标题}\n- 主角姓名：{主角姓名}（{主角性别}，{主角年龄}岁）\n- 故事地点：{故事地点}\n- 字数要求：{字数要求}\n\n【悬疑元素】\n题材类型：{题材类型}\n情节类型：{情节类型}\n情绪氛围：{情绪氛围}\n时间背景：{时间背景}\n\n【创作要求】\n{创作要求}\n\n【参考文本】\n{参考文本}\n\n【输出要求】\n1. 设置引人入胜的谜题或悬案\n2. 布局巧妙的线索和伏笔\n3. 制造紧张悬疑的氛围\n4. 推理过程逻辑严密\n5. 真相揭露令人意外yet合理\n6. 人物心理描写细腻\n7. 节奏紧凑，扣人心弦\n\n请创作一篇完整的悬疑推理短篇小说。");
        suspenseShortStoryGenerator.setTags(Arrays.asList("短篇小说", "悬疑", "推理", "谜题", "完整故事"));
        suspenseShortStoryGenerator.setIsDefault(1);
        suspenseShortStoryGenerator.setUsageCount(0);
        defaultPrompts.add(suspenseShortStoryGenerator);

        // 17. 科幻短篇小说生成器
        Prompt sciFiShortStoryGenerator = new Prompt();
        sciFiShortStoryGenerator.setTitle("科幻短篇小说生成器");
        sciFiShortStoryGenerator.setCategory("short-story");
        sciFiShortStoryGenerator.setDescription("创作充满想象力的科幻类短篇小说");
        sciFiShortStoryGenerator.setContent("请创作一篇科幻背景的短篇小说。\n\n【基础设定】\n- 小说标题：{小说标题}\n- 主角姓名：{主角姓名}（{主角性别}，{主角年龄}岁）\n- 故事地点：{故事地点}\n- 字数要求：{字数要求}\n\n【科幻元素】\n题材类型：{题材类型}\n情节类型：{情节类型}\n情绪氛围：{情绪氛围}\n时间背景：{时间背景}\n\n【创作要求】\n{创作要求}\n\n【参考文本】\n{参考文本}\n\n【输出要求】\n1. 构建合理的未来科技设定\n2. 探讨科技与人性的关系\n3. 情节具有科幻特色和想象力\n4. 专业术语使用恰当\n5. 体现科学原理或哲学思考\n6. 人物面临科技时代的挑战\n7. 传达对未来的思考和启示\n\n请创作一篇完整的科幻短篇小说。");
        sciFiShortStoryGenerator.setTags(Arrays.asList("短篇小说", "科幻", "未来", "科技", "完整故事"));
        sciFiShortStoryGenerator.setIsDefault(1);
        sciFiShortStoryGenerator.setUsageCount(0);
        defaultPrompts.add(sciFiShortStoryGenerator);

        // 18. 通用短篇小说模板
        Prompt universalShortStoryTemplate = new Prompt();
        universalShortStoryTemplate.setTitle("通用短篇小说模板");
        universalShortStoryTemplate.setCategory("short-story");
        universalShortStoryTemplate.setDescription("适用于各种题材的通用短篇小说创作模板");
        universalShortStoryTemplate.setContent("请根据以下设定创作一篇短篇小说。\n\n【基础信息】\n标题：{小说标题}\n主角：{主角姓名}（{主角性别}，{主角年龄}岁）\n地点：{故事地点}\n字数：{字数要求}\n\n【风格设定】\n题材：{题材类型}\n情节：{情节类型}\n氛围：{情绪氛围}\n背景：{时间背景}\n\n【特殊要求】\n{创作要求}\n\n【参考素材】\n{参考文本}\n\n【创作原则】\n1. 开头要抓人，快速进入故事情境\n2. 中间发展要有转折和冲突\n3. 结尾要有深度，给读者思考空间\n4. 人物性格要鲜明立体\n5. 对话要自然流畅\n6. 描写要生动有画面感\n7. 主题积极正面\n\n【固定输出格式】\n## {标题}\n\n{正文内容 - 完整的短篇小说}\n\n---\n字数统计：约{实际字数}字\n\n请严格按照上述要求创作一篇完整的短篇小说。");
        universalShortStoryTemplate.setTags(Arrays.asList("短篇小说", "通用模板", "多题材", "标准格式"));
        universalShortStoryTemplate.setIsDefault(1);
        universalShortStoryTemplate.setUsageCount(0);
        defaultPrompts.add(universalShortStoryTemplate);

        // 19. 综合拆书分析
        Prompt comprehensiveBookAnalysis = new Prompt();
        comprehensiveBookAnalysis.setTitle("综合拆书分析");
        comprehensiveBookAnalysis.setCategory("book-analysis");
        comprehensiveBookAnalysis.setDescription("全方位分析小说的写作技法、结构特点和创作亮点");
        comprehensiveBookAnalysis.setContent("请对以下小说文本进行深度拆书分析：\n\n【分析文本】\n{小说文本}\n\n【分析要求】\n请从以下维度进行详细分析：\n\n1. 基础信息统计\n2. 文体特征分析  \n3. 结构技法解析\n4. 人物塑造手法\n5. 语言风格特色\n6. 情节推进技巧\n7. 可学习的写作亮点\n8. 具体创作建议\n\n【输出格式】\n请以文本形式输出完整的分析报告，包含具体的技法解析和可借鉴的创作要点。");
        comprehensiveBookAnalysis.setTags(Arrays.asList("拆书", "综合分析", "写作技法", "创作指导"));
        comprehensiveBookAnalysis.setIsDefault(1);
        comprehensiveBookAnalysis.setUsageCount(0);
        defaultPrompts.add(comprehensiveBookAnalysis);

        // 20. 结构分析专项
        Prompt structureAnalysisSpecial = new Prompt();
        structureAnalysisSpecial.setTitle("结构分析专项");
        structureAnalysisSpecial.setCategory("book-analysis");
        structureAnalysisSpecial.setDescription("专注分析小说的章节结构、情节布局和叙事节奏");
        structureAnalysisSpecial.setContent("请对以下小说文本进行结构专项分析：\n\n【分析文本】\n{小说文本}\n\n【分析重点】\n重点关注以下结构要素：\n\n1. 章节划分逻辑\n2. 情节发展节奏\n3. 冲突设置技巧\n4. 悬念布局方式\n5. 转折点设计\n6. 开头结尾呼应\n7. 线索铺设手法\n\n【输出要求】\n以文本形式详细分析结构特点，提供具体的章节组织建议和情节推进技巧。");
        structureAnalysisSpecial.setTags(Arrays.asList("拆书", "结构分析", "情节布局", "叙事技巧"));
        structureAnalysisSpecial.setIsDefault(1);
        structureAnalysisSpecial.setUsageCount(0);
        defaultPrompts.add(structureAnalysisSpecial);

        // 21. 人物塑造分析
        Prompt characterAnalysis = new Prompt();
        characterAnalysis.setTitle("人物塑造分析");
        characterAnalysis.setCategory("book-analysis");
        characterAnalysis.setDescription("深度分析小说中的人物设定、性格刻画和关系处理");
        characterAnalysis.setContent("请对以下小说文本进行人物塑造专项分析：\n\n【分析文本】\n{小说文本}\n\n【分析维度】\n请重点分析：\n\n1. 主要人物特征\n2. 性格塑造手法\n3. 人物关系网络\n4. 角色成长弧线\n5. 对话个性化处理\n6. 心理描写技巧\n7. 人物功能定位\n\n【输出格式】\n以文本形式提供详细的人物分析报告，包含具体的人物塑造技法和创作启发。");
        characterAnalysis.setTags(Arrays.asList("拆书", "人物分析", "性格塑造", "角色设计"));
        characterAnalysis.setIsDefault(1);
        characterAnalysis.setUsageCount(0);
        defaultPrompts.add(characterAnalysis);

        // 22. 语言风格分析
        Prompt languageStyleAnalysis = new Prompt();
        languageStyleAnalysis.setTitle("语言风格分析");
        languageStyleAnalysis.setCategory("book-analysis");
        languageStyleAnalysis.setDescription("分析小说的文字风格、修辞手法和语言特色");
        languageStyleAnalysis.setContent("请对以下小说文本进行语言风格专项分析：\n\n【分析文本】\n{小说文本}\n\n【分析角度】\n请从以下角度分析：\n\n1. 整体文风特征\n2. 句式结构特点\n3. 修辞手法运用\n4. 词汇选择倾向\n5. 语言节奏感\n6. 表达技巧亮点\n7. 文字画面感营造\n\n【输出要求】\n以文本形式提供详细的语言分析，包含具体的写作技法解析和文风借鉴建议。");
        languageStyleAnalysis.setTags(Arrays.asList("拆书", "语言分析", "文风特色", "修辞技巧"));
        languageStyleAnalysis.setIsDefault(1);
        languageStyleAnalysis.setUsageCount(0);
        defaultPrompts.add(languageStyleAnalysis);

        // 23. 情节技巧分析
        Prompt plotTechniqueAnalysis = new Prompt();
        plotTechniqueAnalysis.setTitle("情节技巧分析");
        plotTechniqueAnalysis.setCategory("book-analysis");
        plotTechniqueAnalysis.setDescription("专注分析情节推进、冲突设置和戏剧张力营造");
        plotTechniqueAnalysis.setContent("请对以下小说文本进行情节技巧专项分析：\n\n【分析文本】\n{小说文本}\n\n【分析重点】\n请重点关注：\n\n1. 情节推进方式\n2. 冲突层次设计\n3. 悬念制造技巧\n4. 戏剧张力营造\n5. 转折点处理\n6. 伏笔铺设手法\n7. 高潮段落设计\n\n【输出格式】\n以文本形式详细分析情节技法，提供可学习的创作技巧和具体应用建议。");
        plotTechniqueAnalysis.setTags(Arrays.asList("拆书", "情节分析", "冲突设计", "悬念技巧"));
        plotTechniqueAnalysis.setIsDefault(1);
        plotTechniqueAnalysis.setUsageCount(0);
        defaultPrompts.add(plotTechniqueAnalysis);

        return defaultPrompts;
    }
}
