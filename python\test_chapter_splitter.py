#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
章节切分器单元测试
"""

import unittest
import tempfile
import os
from python.chapter_splitter import ChapterSplitter, Chapter, NovelSplitResult
from python.chapter_validator import ChapterValidator
from python.chapter_patterns import chapter_patterns


class TestChapterSplitter(unittest.TestCase):
    """章节切分器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.splitter = ChapterSplitter()
        self.validator = ChapterValidator()
    
    def test_standard_chinese_chapters(self):
        """测试标准中文章节格式"""
        text = """
        第一章 开始
        这是第一章的内容。
        
        第二章 发展
        这是第二章的内容。
        
        第三章 结束
        这是第三章的内容。
        """
        
        result = self.splitter.split_novel(text, "测试小说")
        
        self.assertEqual(result.total_chapters, 3)
        self.assertEqual(len(result.chapters), 3)
        self.assertEqual(result.chapters[0].title, "开始")
        self.assertEqual(result.chapters[1].title, "发展")
        self.assertEqual(result.chapters[2].title, "结束")
        self.assertGreater(result.confidence, 0.7)
    
    def test_english_chapters(self):
        """测试英文章节格式"""
        text = """
        Chapter 1: The Beginning
        This is the content of chapter 1.
        
        Chapter 2: The Development
        This is the content of chapter 2.
        
        Chapter 3: The End
        This is the content of chapter 3.
        """
        
        result = self.splitter.split_novel(text, "Test Novel")
        
        self.assertEqual(result.total_chapters, 3)
        self.assertEqual(len(result.chapters), 3)
        self.assertIn("Beginning", result.chapters[0].title)
        self.assertGreater(result.confidence, 0.7)
    
    def test_numeric_chapters(self):
        """测试数字章节格式"""
        text = """
        1. 第一个故事
        这是第一个故事的内容。
        
        2. 第二个故事
        这是第二个故事的内容。
        
        3. 第三个故事
        这是第三个故事的内容。
        """
        
        result = self.splitter.split_novel(text, "数字章节测试")
        
        self.assertEqual(result.total_chapters, 3)
        self.assertEqual(len(result.chapters), 3)
        self.assertGreater(result.confidence, 0.6)
    
    def test_mixed_formats(self):
        """测试混合格式"""
        text = """
        第一章 开始
        这是第一章的内容。
        
        Chapter 2: Middle
        This is chapter 2 content.
        
        3. 结束
        这是第三章的内容。
        """
        
        result = self.splitter.split_novel(text, "混合格式测试")
        
        # 应该能识别出至少一种格式
        self.assertGreater(result.total_chapters, 0)
        self.assertGreater(result.confidence, 0.5)
    
    def test_no_clear_chapters(self):
        """测试没有明确章节标识的文本"""
        text = """
        这是一段没有明确章节标识的文本。
        它包含了很多内容，但是没有明显的章节分割。
        系统应该能够处理这种情况，并给出合理的结果。
        """ * 100  # 重复以增加长度
        
        result = self.splitter.split_novel(text, "无章节测试")
        
        # 应该至少有一个章节
        self.assertGreaterEqual(result.total_chapters, 1)
        self.assertGreater(len(result.chapters), 0)
    
    def test_empty_text(self):
        """测试空文本"""
        with self.assertRaises(ValueError):
            self.splitter.split_novel("", "空文本测试")
    
    def test_single_chapter(self):
        """测试单章节文本"""
        text = "这是一个很短的文本，没有章节分割。"
        
        result = self.splitter.split_novel(text, "单章节测试")
        
        self.assertEqual(result.total_chapters, 1)
        self.assertEqual(len(result.chapters), 1)
        self.assertEqual(result.chapters[0].title, "全文")
    
    def test_chapter_positions(self):
        """测试章节位置准确性"""
        text = """第一章 开始
内容1

第二章 中间
内容2

第三章 结束
内容3"""
        
        result = self.splitter.split_novel(text, "位置测试")
        
        # 检查位置不重叠
        for i in range(len(result.chapters) - 1):
            current = result.chapters[i]
            next_chapter = result.chapters[i + 1]
            self.assertLessEqual(current.end_position, next_chapter.start_position)
        
        # 检查内容完整性
        total_content = "".join(ch.content for ch in result.chapters)
        self.assertGreater(len(total_content), 0)


class TestChapterValidator(unittest.TestCase):
    """章节验证器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.validator = ChapterValidator()
        self.splitter = ChapterSplitter()
    
    def test_good_result_validation(self):
        """测试良好结果的验证"""
        text = """
        第一章 开始
        """ + "这是第一章的内容。" * 100 + """
        
        第二章 发展
        """ + "这是第二章的内容。" * 100 + """
        
        第三章 结束
        """ + "这是第三章的内容。" * 100
        
        result = self.splitter.split_novel(text, "良好测试")
        validation = self.validator.validate(result)
        
        self.assertGreater(validation.overall_score, 70)
        self.assertLess(len(validation.issues), 3)
    
    def test_poor_result_validation(self):
        """测试较差结果的验证"""
        # 创建一个有问题的结果
        chapters = [
            Chapter(1, "", 0, 10, "短", 1, 0.5, "test"),  # 空标题，过短
            Chapter(2, "正常章节", 5, 1000, "正常内容" * 100, 500, 0.8, "test"),  # 重叠
            Chapter(3, "超长标题" * 20, 1000, 2000, "内容", 1000, 0.6, "test")  # 超长标题
        ]
        
        result = NovelSplitResult(
            title="问题测试",
            total_chapters=3,
            total_words=2000,
            chapters=chapters,
            split_method="test",
            confidence=0.4,
            warnings=[]
        )
        
        validation = self.validator.validate(result)
        
        self.assertLess(validation.overall_score, 70)
        self.assertGreater(len(validation.issues), 0)
        self.assertGreater(len(validation.suggestions), 0)
    
    def test_statistics_calculation(self):
        """测试统计信息计算"""
        text = """
        第一章 开始
        """ + "内容" * 500 + """
        
        第二章 发展
        """ + "内容" * 600 + """
        
        第三章 结束
        """ + "内容" * 400
        
        result = self.splitter.split_novel(text, "统计测试")
        validation = self.validator.validate(result)
        
        stats = validation.statistics
        self.assertEqual(stats["total_chapters"], 3)
        self.assertGreater(stats["avg_chapter_length"], 0)
        self.assertGreater(stats["total_words"], 0)
        self.assertIn("avg_confidence", stats)


class TestChapterPatterns(unittest.TestCase):
    """章节模式测试类"""
    
    def test_pattern_matching(self):
        """测试模式匹配"""
        test_cases = [
            ("第一章 测试", "standard_chinese"),
            ("Chapter 1: Test", "english_chapter"),
            ("1. 测试", "numeric_chapter"),
            ("一、测试", "chinese_number"),
        ]
        
        for text, expected_pattern in test_cases:
            found = False
            for pattern_obj in chapter_patterns.get_patterns():
                if pattern_obj.pattern.search(text):
                    if pattern_obj.name == expected_pattern:
                        found = True
                        break
            self.assertTrue(found, f"Pattern {expected_pattern} should match '{text}'")
    
    def test_pattern_priority(self):
        """测试模式优先级"""
        patterns = chapter_patterns.get_patterns()
        
        # 检查是否按优先级排序
        for i in range(len(patterns) - 1):
            self.assertGreaterEqual(patterns[i].priority, patterns[i + 1].priority)
    
    def test_custom_pattern(self):
        """测试自定义模式"""
        original_count = len(chapter_patterns.get_patterns())
        
        chapter_patterns.add_custom_pattern(
            "test_pattern",
            r"^测试\d+",
            50,
            "测试模式"
        )
        
        new_count = len(chapter_patterns.get_patterns())
        self.assertEqual(new_count, original_count + 1)


class TestIntegration(unittest.TestCase):
    """集成测试"""
    
    def test_file_processing(self):
        """测试文件处理流程"""
        # 创建临时文件
        test_content = """
        第一章 开始的故事
        这是一个测试小说的第一章内容。
        
        第二章 发展的情节
        这是第二章的内容，情节开始发展。
        
        第三章 高潮部分
        这是第三章，故事达到高潮。
        
        第四章 结局
        这是最后一章，故事圆满结束。
        """
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', 
                                       delete=False, encoding='utf-8') as f:
            f.write(test_content)
            temp_file = f.name
        
        try:
            # 读取文件
            with open(temp_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 处理
            splitter = ChapterSplitter()
            result = splitter.split_novel(content, "集成测试小说")
            
            # 验证
            validator = ChapterValidator()
            validation = validator.validate(result)
            
            # 检查结果
            self.assertEqual(result.total_chapters, 4)
            self.assertGreater(validation.overall_score, 60)
            
            # 导出JSON
            json_content = splitter.export_to_json(result, include_content=False)
            self.assertIn("title", json_content)
            self.assertIn("chapters", json_content)
            
        finally:
            # 清理临时文件
            os.unlink(temp_file)


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)
