import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { novelApi, chapterApi, characterApi } from '@/services/novelApi'
import { ElMessage } from 'element-plus'

export const useNovelStore = defineStore('novel', () => {
  // 状态
  const currentNovel = ref(null)
  const novels = ref([])
  const chapters = ref([])
  const characters = ref([])
  const loading = ref(false)
  
  // 计算属性
  const wordCount = computed(() => {
    if (!currentNovel.value) return 0
    return currentNovel.value.wordCount || 0
  })
  
  const chapterCount = computed(() => {
    return chapters.value.length
  })
  
  // 小说相关方法
  const fetchNovels = async () => {
    try {
      loading.value = true
      const data = await novelApi.getNovelList()
      novels.value = data || []
    } catch (error) {
      console.error('获取小说列表失败:', error)
      ElMessage.error('获取小说列表失败')
    } finally {
      loading.value = false
    }
  }
  
  const fetchNovel = async (id) => {
    try {
      loading.value = true
      const data = await novelApi.getNovel(id)
      currentNovel.value = data
      return data
    } catch (error) {
      console.error('获取小说详情失败:', error)
      ElMessage.error('获取小说详情失败')
      throw error
    } finally {
      loading.value = false
    }
  }
  
  const createNovel = async (novelData) => {
    try {
      loading.value = true
      const data = await novelApi.createNovel(novelData)
      novels.value.unshift(data)
      ElMessage.success('小说创建成功')
      return data
    } catch (error) {
      console.error('创建小说失败:', error)
      ElMessage.error('创建小说失败')
      throw error
    } finally {
      loading.value = false
    }
  }
  
  const updateNovel = async (id, novelData) => {
    try {
      loading.value = true
      const data = await novelApi.updateNovel(id, novelData)
      
      // 更新本地数据
      const index = novels.value.findIndex(n => n.id === id)
      if (index > -1) {
        novels.value[index] = data
      }
      
      if (currentNovel.value && currentNovel.value.id === id) {
        currentNovel.value = data
      }
      
      ElMessage.success('小说更新成功')
      return data
    } catch (error) {
      console.error('更新小说失败:', error)
      ElMessage.error('更新小说失败')
      throw error
    } finally {
      loading.value = false
    }
  }
  
  const deleteNovel = async (id) => {
    try {
      loading.value = true
      await novelApi.deleteNovel(id)
      
      // 从本地数据中移除
      novels.value = novels.value.filter(n => n.id !== id)
      
      if (currentNovel.value && currentNovel.value.id === id) {
        currentNovel.value = null
      }
      
      ElMessage.success('小说删除成功')
    } catch (error) {
      console.error('删除小说失败:', error)
      ElMessage.error('删除小说失败')
      throw error
    } finally {
      loading.value = false
    }
  }
  
  // 章节相关方法
  const fetchChapters = async (novelId) => {
    try {
      loading.value = true
      const data = await chapterApi.getChapters(novelId)
      chapters.value = data || []
    } catch (error) {
      console.error('获取章节列表失败:', error)
      ElMessage.error('获取章节列表失败')
    } finally {
      loading.value = false
    }
  }
  
  const createChapter = async (novelId, chapterData) => {
    try {
      loading.value = true
      const data = await chapterApi.createChapter(novelId, chapterData)
      chapters.value.push(data)
      
      // 更新小说的章节数
      await novelApi.updateWordCount(novelId)
      
      ElMessage.success('章节创建成功')
      return data
    } catch (error) {
      console.error('创建章节失败:', error)
      ElMessage.error('创建章节失败')
      throw error
    } finally {
      loading.value = false
    }
  }
  
  const updateChapter = async (novelId, chapterId, chapterData) => {
    try {
      loading.value = true
      const data = await chapterApi.updateChapter(novelId, chapterId, chapterData)
      
      // 更新本地数据
      const index = chapters.value.findIndex(c => c.id === chapterId)
      if (index > -1) {
        chapters.value[index] = data
      }
      
      // 更新小说的字数统计
      await novelApi.updateWordCount(novelId)
      
      ElMessage.success('章节更新成功')
      return data
    } catch (error) {
      console.error('更新章节失败:', error)
      ElMessage.error('更新章节失败')
      throw error
    } finally {
      loading.value = false
    }
  }
  
  const deleteChapter = async (novelId, chapterId) => {
    try {
      loading.value = true
      await chapterApi.deleteChapter(novelId, chapterId)
      
      // 从本地数据中移除
      chapters.value = chapters.value.filter(c => c.id !== chapterId)
      
      // 更新小说的章节数
      await novelApi.updateWordCount(novelId)
      
      ElMessage.success('章节删除成功')
    } catch (error) {
      console.error('删除章节失败:', error)
      ElMessage.error('删除章节失败')
      throw error
    } finally {
      loading.value = false
    }
  }
  
  // 人物相关方法
  const fetchCharacters = async (novelId) => {
    try {
      const data = await characterApi.getCharacters(novelId)
      characters.value = data || []
    } catch (error) {
      console.error('获取人物列表失败:', error)
      ElMessage.error('获取人物列表失败')
    }
  }
  
  const createCharacter = async (novelId, characterData) => {
    try {
      const data = await characterApi.createCharacter(novelId, characterData)
      characters.value.push(data)
      ElMessage.success('人物创建成功')
      return data
    } catch (error) {
      console.error('创建人物失败:', error)
      ElMessage.error('创建人物失败')
      throw error
    }
  }
  
  return {
    // 状态
    currentNovel,
    novels,
    chapters,
    characters,
    loading,
    
    // 计算属性
    wordCount,
    chapterCount,
    
    // 方法
    fetchNovels,
    fetchNovel,
    createNovel,
    updateNovel,
    deleteNovel,
    fetchChapters,
    createChapter,
    updateChapter,
    deleteChapter,
    fetchCharacters,
    createCharacter
  }
})
