package com.writing.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.writing.entity.NovelGenre;
import com.writing.mapper.NovelGenreMapper;
import com.writing.service.NovelGenreService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 小说类型Service实现类
 */
@Service
public class NovelGenreServiceImpl extends ServiceImpl<NovelGenreMapper, NovelGenre> implements NovelGenreService {

    @Override
    public List<NovelGenre> getGenresByUserId(Long userId) {
        // 获取用户自定义类型
        List<NovelGenre> userGenres = this.list(new LambdaQueryWrapper<NovelGenre>()
                .eq(NovelGenre::getUserId, userId)
                .eq(NovelGenre::getIsDefault, 0)
                .orderByDesc(NovelGenre::getCreatedAt));

        // 获取默认类型
        List<NovelGenre> defaultGenres = getDefaultGenres();

        // 合并列表，默认类型在前
        List<NovelGenre> allGenres = new ArrayList<>();
        allGenres.addAll(defaultGenres);
        allGenres.addAll(userGenres);

        return allGenres;
    }

    @Override
    public NovelGenre getGenreById(Long genreId, Long userId) {
        // 查找指定ID的类型，必须属于该用户（包括用户的默认类型）
        return this.getOne(new LambdaQueryWrapper<NovelGenre>()
                .eq(NovelGenre::getId, genreId)
                .eq(NovelGenre::getUserId, userId));
    }

    @Override
    public NovelGenre getGenreByCode(String code, Long userId) {
        // 首先查找用户自定义的类型
        NovelGenre userGenre = this.getOne(new LambdaQueryWrapper<NovelGenre>()
                .eq(NovelGenre::getCode, code)
                .eq(NovelGenre::getUserId, userId)
                .eq(NovelGenre::getIsDefault, 0));

        if (userGenre != null) {
            return userGenre;
        }

        // 如果没有用户自定义的，查找该用户的默认类型
        return this.getOne(new LambdaQueryWrapper<NovelGenre>()
                .eq(NovelGenre::getCode, code)
                .eq(NovelGenre::getUserId, userId)
                .eq(NovelGenre::getIsDefault, 1));
    }

    @Override
    public NovelGenre createGenre(NovelGenre genre) {
        // 检查代码是否已存在
        NovelGenre existingGenre = getGenreByCode(genre.getCode(), genre.getUserId());
        if (existingGenre != null) {
            throw new RuntimeException("类型代码已存在");
        }

        // 设置为非默认类型
        genre.setIsDefault(0);
        this.save(genre);
        return genre;
    }

    @Override
    public NovelGenre updateGenre(NovelGenre genre) {
        // 验证类型是否存在且属于用户
        NovelGenre existingGenre = getGenreById(genre.getId(), genre.getUserId());
        if (existingGenre == null) {
            throw new RuntimeException("类型不存在");
        }

        // 默认类型不允许修改代码
        if (existingGenre.getIsDefault() == 1 && !existingGenre.getCode().equals(genre.getCode())) {
            throw new RuntimeException("默认类型不允许修改代码");
        }

        // 检查代码是否与其他类型冲突
        if (!existingGenre.getCode().equals(genre.getCode())) {
            NovelGenre conflictGenre = getGenreByCode(genre.getCode(), genre.getUserId());
            if (conflictGenre != null) {
                throw new RuntimeException("类型代码已存在");
            }
        }

        this.updateById(genre);
        return genre;
    }

    @Override
    public boolean deleteGenre(Long genreId, Long userId) {
        NovelGenre genre = getGenreById(genreId, userId);
        if (genre == null) {
            throw new RuntimeException("类型不存在");
        }

        // 默认类型不允许删除
        if (genre.getIsDefault() == 1) {
            throw new RuntimeException("默认类型不允许删除");
        }

        return this.removeById(genreId);
    }

    @Override
    public void initDefaultGenres(Long userId) {
        // 检查是否已经初始化过
        long count = this.count(new LambdaQueryWrapper<NovelGenre>()
                .eq(NovelGenre::getUserId, userId)
                .eq(NovelGenre::getIsDefault, 1));

        if (count > 0) {
            return; // 已经初始化过
        }

        // 创建默认类型
        List<NovelGenre> defaultGenres = getDefaultGenres();
        for (NovelGenre genre : defaultGenres) {
            genre.setUserId(userId);
            this.save(genre);
        }
    }

    @Override
    public List<NovelGenre> getDefaultGenres() {
        List<NovelGenre> defaultGenres = new ArrayList<>();

        // 玄幻
        NovelGenre fantasy = new NovelGenre();
        fantasy.setCode("fantasy");
        fantasy.setName("玄幻");
        fantasy.setDescription("修仙、异世界、法宝、灵气、境界");
        fantasy.setPrompt("创作一部玄幻小说，包含修仙体系、异世界冒险等元素，注重世界观构建和修炼体系描写。");
        fantasy.setExamples("《斗破苍穹》、《完美世界》");
        fantasy.setUsageCount(0);
        fantasy.setColor("#8B5CF6");
        fantasy.setIcon("⚡");
        fantasy.setIsDefault(1);
        defaultGenres.add(fantasy);

        // 都市
        NovelGenre urban = new NovelGenre();
        urban.setCode("urban");
        urban.setName("都市");
        urban.setDescription("都市、现代、职场、生活");
        urban.setPrompt("创作一部都市小说，以现代都市为背景，贴近现实生活，注重人物情感和社会现象描写。");
        urban.setExamples("《何以笙箫默》、《杜拉拉升职记》");
        urban.setUsageCount(0);
        urban.setColor("#10B981");
        urban.setIcon("🏙️");
        urban.setIsDefault(1);
        defaultGenres.add(urban);

        // 历史
        NovelGenre history = new NovelGenre();
        history.setCode("history");
        history.setName("历史");
        history.setDescription("历史、古代、朝廷、战争");
        history.setPrompt("创作一部历史小说，以真实历史为背景，注重历史考证和时代特色描写。");
        history.setExamples("《明朝那些事儿》、《康熙大帝》");
        history.setUsageCount(0);
        history.setColor("#F59E0B");
        history.setIcon("📜");
        history.setIsDefault(1);
        defaultGenres.add(history);

        // 科幻
        NovelGenre scifi = new NovelGenre();
        scifi.setCode("scifi");
        scifi.setName("科幻");
        scifi.setDescription("科幻、未来、科技、太空");
        scifi.setPrompt("创作一部科幻小说，包含未来科技、太空探索等元素，注重科学性和想象力的平衡。");
        scifi.setExamples("《三体》、《银河系漫游指南》");
        scifi.setUsageCount(0);
        scifi.setColor("#3B82F6");
        scifi.setIcon("🚀");
        scifi.setIsDefault(1);
        defaultGenres.add(scifi);

        // 武侠
        NovelGenre wuxia = new NovelGenre();
        wuxia.setCode("wuxia");
        wuxia.setName("武侠");
        wuxia.setDescription("武侠、江湖、武功、侠义");
        wuxia.setPrompt("创作一部武侠小说，以江湖为背景，注重武功描写和侠义精神体现。");
        wuxia.setExamples("《射雕英雄传》、《天龙八部》");
        wuxia.setUsageCount(0);
        wuxia.setColor("#EF4444");
        wuxia.setIcon("⚔️");
        wuxia.setIsDefault(1);
        defaultGenres.add(wuxia);

        // 言情
        NovelGenre romance = new NovelGenre();
        romance.setCode("romance");
        romance.setName("言情");
        romance.setDescription("言情、爱情、情感、浪漫");
        romance.setPrompt("创作一部言情小说，以爱情为主线，注重情感描写和人物关系发展。");
        romance.setExamples("《简爱》、《傲慢与偏见》");
        romance.setUsageCount(0);
        romance.setColor("#EC4899");
        romance.setIcon("💕");
        romance.setIsDefault(1);
        defaultGenres.add(romance);

        return defaultGenres;
    }
}
