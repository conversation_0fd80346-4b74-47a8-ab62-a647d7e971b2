package com.writing.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 小说实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value = "novels", autoResultMap = true)
public class Novel {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    @TableField("user_id")
    private Long userId;
    
    @TableField("title")
    private String title;
    
    @TableField("description")
    private String description;
    
    @TableField("genre")
    private String genre;
    
    @TableField("cover")
    private String cover;
    
    @TableField("status")
    private String status;
    
    @TableField("word_count")
    private Integer wordCount;
    
    @TableField("chapter_count")
    private Integer chapterCount;
    
    @TableField(value = "tags", typeHandler = JacksonTypeHandler.class)
    private List<String> tags;
    
    @TableField("outline")
    private String outline;
    
    @TableField("genre_prompt")
    private String genrePrompt;

    @TableField("processing_status")
    private String processingStatus; // processing, completed, failed

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
    
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
    
    @TableLogic
    @TableField("deleted")
    private Integer deleted;
}
