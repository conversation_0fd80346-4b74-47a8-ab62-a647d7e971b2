package com.writing.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.writing.entity.Corpus;

import java.util.List;

/**
 * 语料库Service接口
 */
public interface CorpusService extends IService<Corpus> {

    /**
     * 获取小说的语料库列表
     */
    List<Corpus> getCorpusByNovelId(Long novelId);

    /**
     * 根据ID获取语料库
     */
    Corpus getCorpusById(Long corpusId, Long novelId);

    /**
     * 创建语料库
     */
    Corpus createCorpus(Corpus corpus);

    /**
     * 更新语料库
     */
    Corpus updateCorpus(Corpus corpus);

    /**
     * 删除语料库
     */
    boolean deleteCorpus(Long corpusId, Long novelId);
}
