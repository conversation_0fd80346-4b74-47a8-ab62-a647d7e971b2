/**
 * 页面缓存管理工具
 * 用于管理 keep-alive 组件的缓存策略
 */

import { ref, watch } from 'vue'

// 缓存的组件列表
export const cachedComponents = ref([
  'HomePage',
  'NovelManagement',
  'PromptsLibrary',
  'ChapterManagement',
  'WritingGoals',
  'TokenBilling',
  'GenreManagement',
  'ToolsLibrary',
  'ShortStory',
  'BookAnalysis',
  'Profile'
])

// 需要实时更新的组件（不缓存）
export const realTimeComponents = [
  'Writer',      // 写作页面需要实时保存
  'Settings',    // 设置页面
  'ApiConfig',   // API配置页面
  'Login'        // 登录页面
]

/**
 * 添加组件到缓存列表
 * @param {string} componentName 组件名称
 */
export function addToCache(componentName) {
  if (!cachedComponents.value.includes(componentName)) {
    cachedComponents.value.push(componentName)
    console.log(`组件 ${componentName} 已添加到缓存`)
  }
}

/**
 * 从缓存列表中移除组件
 * @param {string} componentName 组件名称
 */
export function removeFromCache(componentName) {
  const index = cachedComponents.value.indexOf(componentName)
  if (index > -1) {
    cachedComponents.value.splice(index, 1)
    console.log(`组件 ${componentName} 已从缓存中移除`)
  }
}

/**
 * 清空所有缓存
 */
export function clearAllCache() {
  cachedComponents.value = []
  console.log('所有页面缓存已清空')
}

/**
 * 重置缓存到默认状态
 */
export function resetCache() {
  cachedComponents.value = [
    'HomePage',
    'NovelManagement',
    'PromptsLibrary',
    'ChapterManagement',
    'WritingGoals',
    'TokenBilling',
    'GenreManagement',
    'ToolsLibrary',
    'ShortStory',
    'BookAnalysis',
    'Profile'
  ]
  console.log('页面缓存已重置到默认状态')
}

/**
 * 检查组件是否被缓存
 * @param {string} componentName 组件名称
 * @returns {boolean}
 */
export function isCached(componentName) {
  return cachedComponents.value.includes(componentName)
}

/**
 * 获取缓存统计信息
 * @returns {object}
 */
export function getCacheStats() {
  return {
    cachedCount: cachedComponents.value.length,
    cachedComponents: [...cachedComponents.value],
    realTimeComponents: [...realTimeComponents]
  }
}

/**
 * 根据路由路径判断是否需要缓存
 * @param {string} routePath 路由路径
 * @returns {boolean}
 */
export function shouldCache(routePath) {
  // 路径到组件名的映射
  const pathToComponent = {
    '/': 'HomePage',
    '/novels': 'NovelManagement',
    '/prompts': 'PromptsLibrary',
    '/chapters': 'ChapterManagement',
    '/goals': 'WritingGoals',
    '/billing': 'TokenBilling',
    '/genres': 'GenreManagement',
    '/tools': 'ToolsLibrary',
    '/short-story': 'ShortStory',
    '/book-analysis': 'BookAnalysis',
    '/profile': 'Profile'
  }

  const componentName = pathToComponent[routePath]
  return componentName && isCached(componentName)
}

/**
 * 缓存配置选项
 */
export const cacheConfig = {
  // 最大缓存组件数量
  maxCacheSize: 10,

  // 缓存策略
  strategy: 'lru', // lru: 最近最少使用, fifo: 先进先出

  // 是否启用缓存
  enabled: true,

  // 调试模式
  debug: process.env.NODE_ENV === 'development'
}

/**
 * 打印缓存调试信息
 */
export function debugCache() {
  if (cacheConfig.debug) {
    console.group('📦 Keep-Alive 缓存状态')
    console.log('缓存组件数量:', cachedComponents.value.length)
    console.log('缓存组件列表:', cachedComponents.value)
    console.log('实时组件列表:', realTimeComponents)
    console.log('缓存配置:', cacheConfig)
    console.groupEnd()
  }
}

// 开发环境下自动打印缓存信息
if (process.env.NODE_ENV === 'development') {
  // 监听缓存变化
  watch(cachedComponents, () => {
    debugCache()
  }, { deep: true })
}
